{"externalId": "wf-KPI-COR-ALL-MAIN", "description": "Main workfow used to calculate the kpis from KPIDOM data model", "version": "1_2_0", "schedules": [{"externalId": "wfs-KPI-COR-ALL-MAIN-01", "cron": "0 * * * *", "data": null}], "tasks": [{"externalId": "wft-KPI-COR-ALL-MAIN::kpi-parameter-processor", "type": "function", "dependsOn": [], "parameters": {"externalId": "kpi-parameter-processor", "data": null}}, {"externalId": "wft-KPI-COR-ALL-MAIN::dynamic-01", "type": "dynamic", "dependsOn": ["wft-KPI-COR-ALL-MAIN::kpi-parameter-processor"], "parameters": {"tasks": "${wft-KPI-COR-ALL-MAIN::kpi-parameter-processor.output.response.tasks}"}}, {"externalId": "wft-KPI-COR-ALL-MAIN::kpi-calculator", "type": "function", "dependsOn": ["wft-KPI-COR-ALL-MAIN::dynamic-01"], "parameters": {"externalId": "kpi-calculator", "data": {"kpi_codes": "${wft-KPI-COR-ALL-MAIN::kpi-parameter-processor.output.response.kpi_codes}", "kpi_group": ""}}}]}