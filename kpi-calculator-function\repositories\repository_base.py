from functools import lru_cache
from typing import Any

from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
    DataModel,
    DataModelIdentifier,
    ViewId,
)

from enums.data_model_enum import DataModelEnum
from infra.async_utils import run_sync
from infra.cognite_client_factory import HashableCogniteClient
from infra.env_variables import EnvVariables


class RepositoryBase:
    def __init__(self, cognite_client: CogniteClient, env_vars: EnvVariables) -> None:
        self._cognite_client = cognite_client
        self._env_vars = env_vars
        self._cached_data_models: dict[DataModelEnum, DataModel[ViewId]] = {}

    async def _gql_query_async(
        self,
        query: str,
        variables: dict[str, Any] | None = None,
        data_model: DataModelEnum = DataModelEnum.KpiDOM,
    ):
        return await run_sync(self._gql_query, query, variables, data_model)

    def _gql_query(
        self,
        query: str,
        variables: dict[str, Any] | None = None,
        data_model: DataModelEnum = DataModelEnum.KpiDOM,
    ) -> dict[str, Any]:
        return self._cognite_client.data_modeling.graphql.query(
            id=self._env_vars.cognite.get_data_model_id(data_model),
            query=query,
            variables=variables,
        )

    def _get_data_model(self, data_model: DataModelEnum):
        return get_data_model(
            HashableCogniteClient(self._cognite_client),
            id=self._env_vars.cognite.get_data_model_id(data_model),
        )


@lru_cache
def get_data_model(hashable_client: HashableCogniteClient, id: DataModelIdentifier):
    return hashable_client.client.data_modeling.data_models.retrieve(
        ids=id
    ).latest_version()
