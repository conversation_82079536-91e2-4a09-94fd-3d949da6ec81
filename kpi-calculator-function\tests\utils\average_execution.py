import asyncio
import time
from collections.abc import Awaitable, Callable

from pydantic import BaseModel, Field


class AverageExecutionTime(BaseModel):
    treshold_count: int = 3
    executions: list[float] = Field(default_factory=list)

    @property
    def average(self):
        treshold_index = self.treshold_count - 1
        executions_sorted = sorted(self.executions)[treshold_index:-treshold_index]
        return sum(executions_sorted) / len(executions_sorted)


def get_average_execution_time(
    function: Callable, call_count: int = 10, treshold_count: int = 3
) -> AverageExecutionTime:
    """
    Calls `function` `call_count` times and takes the average response times after
    droping the worst top `treshold_count` worst and best response times
    Args:
        `function`: Callable function to be measured
        `call_count`: How many times the function will be called
        `treshold_count`: How many responses will not be considered (both best and worst response times)
    """
    result = AverageExecutionTime(treshold_count=treshold_count)
    for _ in range(call_count):
        t0 = time.perf_counter()
        function()
        t1 = time.perf_counter()
        elapsed = t1 - t0
        result.executions.append(elapsed)
    return result


async def get_average_execution_time_async(
    async_function: Callable[[], Awaitable],
    call_count: int = 10,
    treshold_count: int = 3,
) -> AverageExecutionTime:
    """
    Calls `async_function` `call_count` times concurrently and measures average response times.

    Args:
        `async_function`: An async function to be measured.
        `call_count`: Number of times the function will be called.
        `treshold_count`: Number of responses to exclude (both best and worst).
    """
    result = AverageExecutionTime(treshold_count=treshold_count)

    async def measure_execution():
        t0 = time.perf_counter()
        await async_function()  # Await the async function
        t1 = time.perf_counter()
        return t1 - t0

    # Run all measurements concurrently
    executions = await asyncio.gather(*(measure_execution() for _ in range(call_count)))

    result.executions.extend(executions)
    return result
