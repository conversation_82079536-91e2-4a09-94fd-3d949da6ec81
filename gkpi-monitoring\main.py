from pathlib import Path

import typer

from gkpi_monitoring.config import Config
from gkpi_monitoring.factories import (
    create_data_monitor_executor,
    create_infra_monitor_executor,
)

app = typer.Typer()

config = Config.from_toml(Path("config.toml"))


@app.command()
def monitor_infra():
    executor = create_infra_monitor_executor(config)
    executor.execute(config.resources)


@app.command()
def monitor_data():
    executor = create_data_monitor_executor(config)
    executor.execute()


if __name__ == "__main__":
    app()
