import logging
from datetime import UTC, datetime


class LoggingService:
    def __init__(self, call_id: str) -> None:
        self.execution_id = call_id

    def info(self, message: str) -> None:
        logging.info(self._format_message(message))

    def error(self, message: str) -> None:
        logging.error(self._format_message(message))

    def warning(self, message: str) -> None:
        logging.warning(self._format_message(message))

    def _format_message(self, message: str) -> str:
        return f"[ExecutionId: {self.execution_id} - Timestamp: {datetime.utcnow().replace(tzinfo=UTC).isoformat()}] - {message}"
