from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

DEFAULT_MODEL_CONFIG = ConfigDict(
    alias_generator=to_camel,
    populate_by_name=True,
    from_attributes=True,
    extra="ignore",
)


class BaseCamelCaseModel(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG


class BaseEntity(BaseCamelCaseModel):
    external_id: str
    space: str


class CodedEntity(BaseEntity):
    code: str
    name: str
    description: str | None = None
