variables:
  - group: kpi-dom
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(PROD_SECRET)
    ${{ else }}:
      value: $(DSD_SECRET)
  - name: pythonEnv
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: qa
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: prod
    ${{ else }}:
      value: dev

stages:
  - stage: PublishCogniteFunction
    displayName: "Publish Function to Cognite for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: PublishFunction
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.11"
            displayName: "Use python 3.11"

          - script: |
              cd $(System.DefaultWorkingDirectory)/cognite-resource-deployment
              pip install -r requirements.txt
            displayName: 'Install deployer dependencies'

          - script: |
              cd $(System.DefaultWorkingDirectory)/cognite-resource-deployment
              python -m deploy_cognite_function $(FUNCTION_NAME)
            displayName: 'Deploy $(FUNCTION_NAME)'
            env:
              AUTH_TOKEN_OVERRIDE: $(tokenOverride)
              AUTH_TEAMS_NOTIFICATION_WEBHOOK_URL: $(TEAMS_HOOK_URL)
              PYTHON_ENV: ${{ variables.pythonEnv }}
              AUTH_SECRET: ${{ variables.authSecret }}
