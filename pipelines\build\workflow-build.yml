trigger: none

variables:
  - group: kpi-dom
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(PROD_SECRET)
    ${{ else }}:
      value: $(DSD_SECRET)
  - name: pythonEnv
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: qa
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: prod
    ${{ else }}:
      value: dev

stages:
- stage: CheckWorkflows
  jobs:
  - job: Check
    displayName: Check Workflows
    steps:
      - task: UsePythonVersion@0
        inputs:
          versionSpec: "3.11"
        displayName: "Use python 3.11"

      - script: |
          cd $(System.DefaultWorkingDirectory)/cognite-resource-deployment
          python -m pip install --upgrade pip
          pip install -r requirements.txt
        displayName: 'Install dependencies'

      - script: |
          cd $(System.DefaultWorkingDirectory)/cognite-resource-deployment
          python -m deploy_cognite_workflow --dry-run
        displayName: 'Validate workflows'
        env:
          AUTH_TOKEN_OVERRIDE: $(tokenOverride)
          PYTHON_ENV: ${{ variables.pythonEnv }}
          AUTH_SECRET: ${{ variables.authSecret }}
