from datetime import UTC, datetime

import pytest

from entities.raw_kpi_parameter import RawKpiParameter
from infra.date_utils import get_execution_window


@pytest.mark.parametrize(
    "executions",
    [
        (
            "* * * * *",  # Every Minute
            [datetime(year=2024, month=12, day=1, hour=1, minute=30, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=5, minute=0, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=12, minute=30, tzinfo=UTC)],
        ),
        (
            "0 * * * *",  # Every Hour
            [datetime(year=2024, month=12, day=1, hour=1, minute=30, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=5, minute=0, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=12, minute=30, tzinfo=UTC)],
        ),
        (
            "0 */3 * * *",  # Every 3 Hours
            [datetime(year=2024, month=12, day=1, hour=3, second=1, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=6, second=0, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=12, second=30, tzinfo=UTC)],
        ),
        (
            "0 0 10 * *",  # Midnight of the 10th day
            [datetime(year=2024, month=12, day=10, hour=0, second=1, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=10, hour=0, minute=59, tzinfo=UTC)],
        ),
    ],
)
def test_param_process_when_due_within_hour(executions: tuple[str, list[datetime]]):
    """
    Tests if a parameter is due since the last hour until now
    """
    r = RawKpiParameter(
        code="foo",
        kpi="bar",
        ingestion_transformation="baz",
        lowest_business_level="qux",
        is_protected=True,
        date_range_refresh=1,
        ingestion_frequency=executions[0],
    )
    execution_times = executions[1]
    for execution_time in execution_times:
        start_time, end_time = get_execution_window(execution_time, 1)
        is_due = r.is_due(start_time, end_time)
        assert is_due


@pytest.mark.parametrize(
    "executions",
    [
        (
            "0 */3 * * *",  # Every 3 Hours
            [datetime(year=2024, month=12, day=1, hour=2, second=1, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=4, second=1, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=1, hour=13, second=1, tzinfo=UTC)],
        ),
        (
            "0 0 10 * *",  # Midnight of the 10th day
            [datetime(year=2024, month=12, day=9, hour=23, minute=59, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=9, hour=23, minute=20, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=10, hour=0, minute=59, tzinfo=UTC)],
            [datetime(year=2024, month=12, day=11, hour=0, minute=0, tzinfo=UTC)],
        ),
    ],
)
def test_param_not_process_when_not_due_within_hour(
    executions: tuple[str, list[datetime]],
):
    """
    Tests if a parameter is not due since the last hour until now
    """
    r = RawKpiParameter(
        code="foo",
        kpi="bar",
        ingestion_transformation="baz",
        lowest_business_level="qux",
        is_protected=True,
        date_range_refresh=1,
        ingestion_frequency=executions[0],
    )
    execution_times = executions[1]
    for execution_time in execution_times:
        start_time, end_time = get_execution_window(execution_time, 1)
        is_due = r.is_due(start_time, end_time)
        assert not is_due
