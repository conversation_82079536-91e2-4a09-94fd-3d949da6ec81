from datetime import UTC, datetime, timedelta


def today():
    now = datetime.now(UTC)
    return now.replace(hour=0, minute=0, second=0, microsecond=0)


def to_start_of_hour(date: datetime) -> datetime:
    return date.replace(minute=0, second=0, microsecond=0, tzinfo=UTC)


def add_hours(date: datetime, hours: int) -> datetime:
    return date + timedelta(hours=hours)


def add_days(date: datetime, days: int) -> datetime:
    return date + timedelta(days=days)


def get_execution_window(
    execution_time: datetime,
    interval_in_hours: int,
) -> tuple[datetime, datetime]:
    start_time = to_start_of_hour(add_hours(execution_time, -interval_in_hours))
    end_time = to_start_of_hour(execution_time)
    return start_time, end_time


def dt_format(date: datetime, include_time: bool = False) -> str:
    return date.strftime("%Y-%m-%d %H:%M:%S" if include_time else "%Y-%m-%d")
