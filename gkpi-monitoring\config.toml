[resources]
functions = ["gkpi-kpi-calculator"]
transformations = [
    "RAW:REF-ALL-REFDATA:KPR-KPIParameter:ingestionTransformation",
    "tr-OEC-COR-ALL-IMP",
    "tr-OEC-COR-ALL-WHR",
    "tr-SAQ-COR-ALL-FPY",
    "tr-SAQ-COR-ALL-QN",
    "tr-SAQ-COR-ALL-GBS",
    "tr-SAQ-COR-ALL-CACT",
    "tr-SAQ-COR-ALL-RMBS",
    "tr-SAQ-COR-ALL-CBS",
    "tr-SAQ-COR-ALL-FGBS",
    "tr-SAQ-COR-ALL-FPY-DAILY",
    "tr-SAQ-COR-ALL-FPY-MONTHLY",
    "tr-EXEC-COR-ALL-PCM",
    "tr-EXEC-COR-ALL-CVR",
    "tr-EXEC-COR-ALL-VVR",
    "tr-EXEC-COR-ALL-DKG",
    "tr-EXEC-COR-ALL-PRD",
]
workflows = [
    "wf-EXEC-COR-ALL-BPC-PLANT",
    "wf-EXEC-COR-ALL-PCM",
    "wf-EXEC-COR-ALL-PRD",
    "wf-KPI-COR-ALL-MAIN",
]


[notification]
teams_url = "https://radixengazure.webhook.office.com/webhookb2/d3f9e2e8-17b7-417d-9714-92bededa2ccd@9339fb1c-0944-4fb9-808d-a278e53590e5/IncomingWebhook/307a3361238642828747fd5a5370ddda/07b9afb6-58ae-4a67-ab14-28a96aa7cdc8/V2352TXpUXVCcd5t0FVJtYKmEeQPUI5Ht-HVR4mk44l4Y1"
teams_notification_infra_template = "templates/infra_notification_issues.txt"
teams_notification_data_template = "templates/data_notification_issues.txt"

[cognite]
client_name = "gkpi-monitoring"
project = "${var.COGNITE_PROJECT}"
base_url = "https://az-eastus-1.cognitedata.com/"
default_space = "GKPI-COR-ALL-DAT"

[cognite.credentials.client_credentials]
client_id = "${var.COGNITE_CLIENT_ID}"
client_secret = "${var.COGNITE_CLIENT_SECRET}"
token_url = "https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37/oauth2/v2.0/token"
scopes = ["https://az-eastus-1.cognitedata.com/.default"]


[data_test]
output_path = "temp"
force_update_cache = false

[[data_test.data_sources]]
space = "INC-STEWARDSHIP-SP"
table = "INC-INCIDENT-SP-LIST"
origin = "raw"
alias = "incident_sp_list"

[[data_test.data_sources]]
space = "INC-STEWARDSHIP-SP"
table = "INC-MINOR-INJ-MASTER-FILE"
origin = "raw"
alias = "minor_inj_master"

[[data_test.data_sources]]
space = "REF-ALL-REFDATA"
table = "EnablonMappingCategories"
origin = "raw"
alias = "enablon_mapping"

[[data_test.data_sources]]
space = "REF-ALL-REFDATA"
table = "STS-Sites"
origin = "raw"
alias = "sites"

[[data_test.data_sources]]
space = "REF-ALL-REFDATA"
table = "UNT-Units (Innoart)"
origin = "raw"
alias = "units"

[[data_test.data_sources]]
space = "REF-ALL-REFDATA"
table = "LOC-Locations"
origin = "raw"
alias = "locations"

[[data_test.data_sources]]
space = "CRTY-COR"
table = "INC-SAFETYINCIDENT"
origin = "raw"
alias = "safety_incident"

[[data_test.data_sources]]
space = "ENA-COR"
table = "INC-Incident"
origin = "raw"
alias = "incident"

[[data_test.data_sources]]
space = "SWP-COR-ALL-DMD"
table = "SwpIncidentImpact"
origin = "fdm"
alias = "swp_incident_impact"

[[data_test.data_sources]]
space = "EDG-COR-ALL-DMD"
table = "BlockStock"
origin = "fdm"
alias = "local_blockstock"

[[data_test.validations]]
description = "validate mapping impact subtype"
sql_file = "validations/validate_mapping_impact_subtype.sql"


[[data_test.validations]]
description = "validate mapping impact levels"
sql_file = "validations/validate_mapping_impact_levels.sql"

[[data_test.validations]]
description = "validate incident cority sites"
sql_file = "validations/validate_incident_cority_sites.sql"

[[data_test.validations]]
description = "validate incident enablon sites"
sql_file = "validations/validate_incident_enablon_sites.sql"

[[data_test.validations]]
description = "validate incident category count"
sql_file = "validations/validate_incident_category_count.sql"

[[data_test.validations]]
description = "validate quality blockstock"
sql_file = "validations/validate_quality_blockstock.sql"