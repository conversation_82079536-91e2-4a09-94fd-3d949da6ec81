from datetime import UTC, datetime
from typing import Any

from pydantic import BaseModel

from infra.env_variables import get_env_variables


class HandlerInput(BaseModel):
    execution_time: datetime
    interval_in_hours: int = 1
    is_workflow_task: bool = True
    process_all: bool = False
    dry_run: bool = False
    debug: bool = False

    @classmethod
    def parse(cls, data: dict[str, Any] | None = None) -> "HandlerInput":
        env_vars = get_env_variables()
        data = data if data else {}
        execution_interval_in_hours = data.get(
            "interval_in_hours", env_vars.cognite.function_interval_in_hours
        )
        execution_time_str = data.get("execution_time", None)
        execution_time = (
            datetime.now(UTC)
            if not execution_time_str
            else datetime.fromisoformat(execution_time_str)
        )
        return HandlerInput(
            execution_time=execution_time,
            interval_in_hours=execution_interval_in_hours,
            is_workflow_task=data.get("is_workflow_task", True),
            process_all=data.get("process_all", False),
            dry_run=data.get("dry_run", False),
            debug=data.get("debug", False),
        )
