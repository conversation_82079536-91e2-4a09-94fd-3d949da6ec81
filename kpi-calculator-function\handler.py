import asyncio
import time
import traceback
import warnings
from datetime import UTC, datetime, timedelta
from typing import Any

import numpy as np
import pandas as pd
from loguru import logger
from pandas.errors import PerformanceWarning

from entities.teams_notification_input import TeamsNotificationInput
from infra.cognite_client_factory import create_cognite_client
from infra.env_variables import get_env_variables
from infra.graphql_client_factory import GraphqlClientFactory
from infra.logger import config_logger
from infra.ms_teams_client import create_ms_teams_client
from repositories.industrial_model_repository import IndustrialModelRepository
from services.graphql_service import GraphqlService
from services.kpidom_service import KPIDOMService


def handle(data: dict[str, Any] | None = None) -> None:
    """Cognite handle function"""

    data = data or {}
    kpi_codes = data.get("kpi_codes", [])
    kpi_group = data.get("kpi_group", "")
    year = data.get("year", datetime.now(UTC).year)

    config_logger("DEBUG" if data.get("debug", False) else "INFO")

    logger.info(f"Input Data: {data}")
    try:
        kpis_oee = []
        kpis_filtered = []
        for kpi_code in kpi_codes:
            if kpi_code in ["AVL", "PFM", "QAT", "OEE", "TEEP"]:
                kpis_oee.append(kpi_code)
            else:
                kpis_filtered.append(kpi_code)

        if len(kpis_filtered) > 0:
            asyncio.run(update_kpidom(year, kpis_filtered, kpi_group, "sum"))
        if len(kpis_oee) > 0:
            asyncio.run(update_kpidom(year, kpis_oee, kpi_group, "average"))

    except Exception as e:
        send_error_message(data)
        raise e


async def update_kpidom(year: int, kpi_codes: list[str], kpi_group: str, calc: str):
    """Update kpidom timeseries results"""

    cognite_client = create_cognite_client()
    env_variables = get_env_variables()
    graphql_client = GraphqlClientFactory.create(cognite_client, env_variables.cognite)
    graphql_service = GraphqlService(graphql_client)
    industrial_model_repository = IndustrialModelRepository(cognite_client)
    kpidom = KPIDOMService(graphql_service, industrial_model_repository)

    if kpi_codes:
        kpi_result_list = kpidom.get_kpis_by_codes(kpi_codes)
    elif kpi_group:
        kpi_result_list = kpidom.get_kpis_by_group(kpi_group)
    else:
        return logger.warning("Missing kpi_code or kpi_group")

    kpi_parameters_ids = []
    kpi_codes_to_update = []

    for kpi in kpi_result_list:
        if kpi.kpi.code not in kpi_codes_to_update:
            kpi_codes_to_update.append(kpi.kpi.code)
        for kpi_parameter in kpi.parameter_values:
            for value in kpi_parameter.values:
                if value and value not in kpi_parameters_ids:
                    kpi_parameters_ids.append(value)

    views = cognite_client.data_modeling.views.retrieve(
        (env_variables.cognite.kpi_data_model_space, "Kpi")
    )
    views.data.sort(key=lambda x: x.__getattribute__("created_time"))
    version = views[-1].version
    entry_kpis = await kpidom.get_entries_updated_kpis_(kpi_codes_to_update, version)
    await graphql_service.cleanup()

    today = datetime.now(UTC)
    if year == today.year:
        if today.month <= 3:
            month_quarter = 4
        elif today.month <= 6:
            month_quarter = 7
        elif today.month <= 9:
            month_quarter = 10
        else:
            month_quarter = 1
        time_granularity_mapper = [
            {
                "code": "ANL",
                "granularity": "y",
                "start": datetime(year, 1, 1, tzinfo=UTC),
                "end": datetime(year + 1, 1, 1, tzinfo=UTC),
            },
            {
                "code": "QRT",
                "granularity": "q",
                "start": datetime(year - 1, month_quarter, 1, tzinfo=UTC),
                "end": datetime(year, month_quarter, 1, tzinfo=UTC),
            },
            {
                "code": "MON",
                "granularity": "mo",
                "start": datetime(year - 1, today.month, 1, tzinfo=UTC),
                "end": datetime(year, today.month + 1, 1, tzinfo=UTC),
            },
            {
                "code": "WKY",
                "granularity": "w",
                "start": adjust_to_previous_monday(
                    datetime(year - 1, today.month, 1, tzinfo=UTC)
                ),
                "end": adjust_to_previous_sunday(
                    datetime(year, today.month, today.day, tzinfo=UTC)
                ),
            },
            {
                "code": "DAI",
                "granularity": "d",
                "start": datetime(year - 1, today.month, 1, tzinfo=UTC),
                "end": datetime(year, today.month, today.day, tzinfo=UTC),
            },
        ]
    else:
        time_granularity_mapper = [
            {
                "code": "ANL",
                "granularity": "y",
                "start": datetime(year, 1, 1, tzinfo=UTC),
                "end": datetime(year + 1, 1, 1, tzinfo=UTC),
            },
            {
                "code": "QRT",
                "granularity": "q",
                "start": datetime(year, 1, 1, tzinfo=UTC),
                "end": datetime(year + 1, 1, 1, tzinfo=UTC),
            },
            {
                "code": "MON",
                "granularity": "mo",
                "start": datetime(year, 1, 1, tzinfo=UTC),
                "end": datetime(year + 1, 1, 1, tzinfo=UTC),
            },
            {
                "code": "WKY",
                "granularity": "w",
                "start": adjust_to_previous_monday(datetime(year, 1, 1, tzinfo=UTC)),
                "end": adjust_to_previous_sunday(datetime(year + 1, 1, 1, tzinfo=UTC)),
            },
            {
                "code": "DAI",
                "granularity": "d",
                "start": datetime(year, 1, 1, tzinfo=UTC),
                "end": datetime(year + 1, 1, 1, tzinfo=UTC),
            },
        ]

    for time_granularity in time_granularity_mapper:
        logger.info(f"Processing granularity {time_granularity['code']}")
        kpi_formulas_expressions = ""
        kpi_results_ids = []
        column_mapper = {}

        for kpi in kpi_result_list:
            if kpi.time_granularity.code == time_granularity["code"]:
                kpi_results_ids.append(kpi.value)
                column_mapper[
                    f"BACKTICK_QUOTED_STRING_{kpi.value.replace(':', '_COLON_')}"
                ] = kpi.value
                formulas_expression = f"`{kpi.value}`={kpi.kpi.formula}\n"
                for kpi_parameter in kpi.parameter_values:
                    values = []
                    for value in kpi_parameter.values:
                        if value is None or value == "KPI-PARAM:NA":
                            formulas_expression = ""
                            if kpi.value in kpi_results_ids:
                                kpi_results_ids.remove(kpi.value)
                                column_mapper.pop(
                                    f"BACKTICK_QUOTED_STRING_{kpi.value.replace(':', '_COLON_')}"
                                )
                        else:
                            values.append(f"`{value}|{calc}`")

                    formulas_expression = formulas_expression.replace(
                        f"{{{kpi_parameter.parameter.code}}}",
                        f"({'+'.join(values)})",
                    )

                kpi_formulas_expressions += formulas_expression

        # file = open("out.txt", "at")
        # file.write(kpi_formulas_expressions)
        # file.close

        if len(kpi_formulas_expressions) > 0:
            dps = cognite_client.time_series.data.retrieve_dataframe(
                external_id=kpi_parameters_ids,
                start=time_granularity["start"],
                end=time_granularity["end"],
                aggregates=calc,
                granularity="1" + time_granularity["granularity"],
                timezone="UTC",
            ).fillna(0)

            warnings.simplefilter("ignore", PerformanceWarning)
            dps.eval(kpi_formulas_expressions, inplace=True)
            dps.index = dps.index + pd.Timedelta(hours=12)
            dps.rename(columns=column_mapper, inplace=True)

            df = dps[kpi_results_ids].replace([np.inf, -np.inf], 0).fillna(0)
            logger.info(f"Saving {df.size} datapoints")
            cognite_client.time_series.data.insert_dataframe(df)

    cognite_client.data_modeling.instances.apply(nodes=entry_kpis)
    return ""


def adjust_to_previous_monday(start_time: datetime) -> datetime:
    weekday = start_time.weekday()
    if weekday != 0:
        days_to_subtract = weekday
        start_time = start_time - timedelta(days=days_to_subtract)

    return start_time.replace(hour=0, minute=0, second=0, microsecond=0)


def adjust_to_previous_sunday(end_time: datetime) -> datetime:
    weekday = end_time.weekday()
    if weekday != 6:
        days_to_subtract = weekday + 1
        end_time = end_time - timedelta(days=days_to_subtract)

    return end_time.replace(hour=23, minute=59, second=0, microsecond=0)


def send_error_message(input: dict[str, Any]):
    ms_teams_client = create_ms_teams_client()
    exc_msg = traceback.format_exc().replace("\n", "<br>")
    msg = f"""
<b>Input:</b><br>
{input}<br>
<br>
<b>Stacktrace:</b><br>
{exc_msg}"""
    asyncio.run(
        ms_teams_client.send_message_async(
            TeamsNotificationInput(
                title="KPI Calculator",
                text=msg,
                activity_messages=[],
            )
        )
    )


if __name__ == "__main__":
    start_total = time.perf_counter()

    # start_time = time.perf_counter()
    # handle({"kpi_codes": [], "kpi_group": "Stewardship"})
    # print(f"Elapsed: {round(time.perf_counter() - start_time, 1)}s")

    print(f"Elapsed: {round(time.perf_counter() - start_total, 1)}s")
