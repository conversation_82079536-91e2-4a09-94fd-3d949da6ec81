import uuid

from cognite.client import CogniteClient
from cognite.client.data_classes import (
    FunctionCall<PERSON>ist,
    TransformationJobList,
    WorkflowExecutionList,
)

from gkpi_monitoring.config import ResourcesConfig
from gkpi_monitoring.file_manager import FileManager
from gkpi_monitoring.models import (
    RawStatement,
    ResourceExecution,
    ResourceMetrics,
    ResourceType,
)
from gkpi_monitoring.notification import NotificationService
from gkpi_monitoring.template import TemplateService


class InfraMonitorExecutor:
    def __init__(
        self,
        cognite_client: CogniteClient,
        file_manager: FileManager,
        notification_service: NotificationService,
        template_service: TemplateService,
    ):
        self._cognite_client = cognite_client
        self._file_manager = file_manager
        self._notification_service = notification_service
        self._template_service = template_service

    def execute(self, request: ResourcesConfig) -> None:
        function_result = self._get_resource_metrics(
            self._resolve_values(request.functions), "function"
        )
        transformation_result = self._get_resource_metrics(
            self._resolve_values(request.transformations), "transformation"
        )
        workflows_result = self._get_resource_metrics(
            self._resolve_values(request.workflows), "workflow"
        )
        data = function_result + transformation_result + workflows_result
        self._notify(data)

    def _notify(self, data: list[ResourceMetrics]) -> None:
        final_result = [
            item.model_dump(mode="python")
            for item in data
            if item.healthy_status != "HEALTHY"
        ]

        if not final_result:
            return

        file_response = self._file_manager.upload_file(
            str(uuid.uuid4()), final_result, "csv"
        )

        message = self._template_service.render_teams_notification_infra(
            len(final_result), file_response
        )

        print(message)

        self._notification_service.send_teams_message(message)

    def _resolve_values(self, values: list[str]) -> list[str]:
        result: set[str] = set()
        for value in values:
            raw_statement = RawStatement.from_statement(value)
            if not raw_statement:
                result.add(value)
            else:
                result.update(self._find_raw_values(raw_statement))
        return sorted(result)

    def _find_raw_values(self, raw_statement: RawStatement) -> list[str]:
        items = self._cognite_client.raw.rows.list(
            raw_statement.database,
            raw_statement.table,
            columns=[raw_statement.column],
            limit=-1,
        )

        return [
            item[raw_statement.column]
            for item in items
            if raw_statement.column in item
            and isinstance(item[raw_statement.column], str)
        ]

    def _get_resource_metrics(
        self, external_ids: list[str], resource_type: ResourceType
    ) -> list[ResourceMetrics]:
        if not external_ids:
            return []
        metrics: list[ResourceMetrics] = []
        for external_id in external_ids:
            execution_status_list = self._get_execution_status(
                external_id, resource_type
            )

            entry = ResourceMetrics.from_resource_executions(
                external_id, resource_type, execution_status_list
            )

            metrics.append(entry)

        return metrics

    def _get_execution_status(
        self, external_id: str, resource_type: ResourceType
    ) -> list[ResourceExecution]:
        limit = 5
        result: (
            TransformationJobList | FunctionCallList | WorkflowExecutionList | None
        ) = None
        if resource_type == "transformation":
            result = self._cognite_client.transformations.jobs.list(
                transformation_external_id=external_id, limit=limit
            )
        elif resource_type == "function":
            result = self._cognite_client.functions.calls.list(
                function_external_id=external_id, limit=limit
            )
        elif resource_type == "workflow":
            wf_version = self._cognite_client.workflows.versions.list(external_id)
            result = self._cognite_client.workflows.executions.list(
                workflow_version_ids=wf_version.as_ids(), limit=limit
            )

        if result is None:
            raise NotImplementedError(f"Resource {resource_type} not implemented")

        items = [ResourceExecution.from_cognite_resource(item) for item in result]
        return [item for item in items if item]
