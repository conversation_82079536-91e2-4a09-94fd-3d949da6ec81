import asyncio
from datetime import datetime

from cognite.client import CogniteClient
from cognite.client.data_classes import TransformationJobStatus
from loguru import logger

from entities.handler_input import HandlerInput
from entities.processor_result import ProcessorResult
from entities.raw_kpi_parameter import RawKpiParameter
from entities.workflow_task_factory import WorkflowTaskFactory
from infra.date_utils import add_days, dt_format, get_execution_window, today
from infra.env_variables import EnvVariables
from repositories.kpi_parameter_repository import KpiParameterRepository
from repositories.time_series_repository import TimeSeriesRepository


class KpiParameterProcessorService:
    """
    Service class for the KPI Parameter Processor.

    This service is responsible for:
        - Getting all the parameters from the raw table;
        - Clearing the time series data points for the parameters that are due;
        - Running the transformations for the parameters that are due.

    Attributes:
        _cognite_client: The `CogniteClient` instance to use.
        _env_vars: The `EnvVariables` to use.
        _kpi_parameter_repository: The `KpiParameterRepository` instance to use.
        _time_series_repository: The `TimeSeriesRepository` instance to use.
        _dry_run: If True, the service will not make any changes
        _execution_interval_in_hours: Interval in hours to consider.
            This is used to determine if the cron execution for a parameter is due.
        _process_all: If True, all parameters will be processed.
        is_workflow_task: If true, the function will return the transformation tasks
            that need to be run after the parameter processor and the kpis
            that need to be calculated by the kpi-processor.
            If False, the processor will execute and wait for the transformations and
            call the kpi-calculator right after that.

    """

    def __init__(
        self,
        client: CogniteClient,
        env_vars: EnvVariables,
        handler_input: HandlerInput,
    ) -> None:
        self._cognite_client = client
        self._env_vars = env_vars
        self._kpi_parameter_repository = KpiParameterRepository(client, env_vars)
        self._time_series_repository = TimeSeriesRepository(client)
        self._execution_interval_in_hours = handler_input.interval_in_hours
        self._process_all = handler_input.process_all
        self._dry_run = handler_input.dry_run
        self._is_workflow_task = handler_input.is_workflow_task

    async def execute_async(self, execution_time: datetime) -> ProcessorResult | None:
        """
        Execute the KPI Parameter Processor service to clear and refill all the due
        parameter time series data points and call kpi-calculator to recalculate all
        affected KPIs

        Parameters:
            execution_time: The `datetime` this execution is being run.
        """
        raw_parameters_due = await self._get_parameters_due_async(execution_time)

        if self._is_workflow_task:
            return ProcessorResult(
                kpi_codes=list({k for p in raw_parameters_due for k in p.kpi_codes}),
                tasks=WorkflowTaskFactory.from_parameters(raw_parameters_due),
            )

        await self._run_parameters_transformations_async(raw_parameters_due)
        self._fire_and_forget_kpi_calculator(raw_parameters_due)

    async def _get_parameters_due_async(self, execution_time: datetime):
        start, end = get_execution_window(
            execution_time, self._execution_interval_in_hours
        )
        raw_params = await self._kpi_parameter_repository.get_all_raw_async()
        due_parameters = [
            p for p in raw_params if p.is_due(start, end) or self._process_all
        ]
        logger.info(
            f"Found {len(due_parameters)} parameter(s) due that will be processed"
        )
        return due_parameters

    async def _clear_parameter_time_series_async(
        self, raw_parameter: RawKpiParameter
    ) -> None:
        """
        Clear all the time series data points for the given parameter.

        Parameters:
            raw_parameter: The `RawKpiParameter` to clear the time series data points.
        """
        if raw_parameter.date_range_refresh <= 0:
            return

        time_series = await self._time_series_repository.get_by_parameter_code_async(
            {raw_parameter.code}
        )
        if not time_series:
            return logger.warning(
                f"No time series found for parameter {raw_parameter.code}"
            )

        delete_to = today()
        delete_from = add_days(delete_to, -raw_parameter.date_range_refresh)
        logger.info(
            f"{raw_parameter.date_range_refresh} days will be erased from "
            + f"{len(time_series)} time series for parameter {raw_parameter.code}. "
            + f"From {dt_format(delete_from)} to {dt_format(delete_to)}"
        )
        if not self._dry_run:
            ids = {t.id for t in time_series}
            logger.info(f"Clearing datapoints from time series {ids}")
            await self._time_series_repository.delete_data_points_async(
                ids, delete_from, delete_to
            )

    async def _run_parameters_transformations_async(
        self, raw_parameters: list[RawKpiParameter]
    ) -> None:
        """
        Run all unique transformations for the given parameters.
        If the transformation takes longer than the timeout, its last status will be
        printed as a warning

        Parameters:
            raw_parameters: The list of `RawKpiParameter` to run the transformations.
        """
        unique_transformation_external_ids = {
            p.ingestion_transformation for p in raw_parameters
        }
        for t in unique_transformation_external_ids:
            logger.debug(f"Running transformation {t}")

        if self._dry_run:
            return

        logger.info("Waiting for transformations...")
        tasks_results = await asyncio.gather(
            *[
                self._cognite_client.transformations.run_async(
                    transformation_external_id=t,
                    timeout=self._env_vars.cognite.transformation_wait_time,
                )
                for t in unique_transformation_external_ids
            ]
        )
        tasks_not_completed = [
            t for t in tasks_results if t.status != TransformationJobStatus.COMPLETED
        ]
        for t in tasks_not_completed:
            logger.warning(
                f"Transformation {t.transformation_external_id} not completed. "
                + f"Current Status: {t.status.value if t.status else 'Unknown'}"
            )

    def _fire_and_forget_kpi_calculator(
        self, raw_parameters: list[RawKpiParameter]
    ) -> None:
        """
        Call the kpi-calculator function passing all unique kpi codes from the
        `raw_parameters` passed in as arguments.
        *Note*: The function call is not waited.

        Parameters:
            raw_parameters: List of `RawKpiParameter` that need their KPIs to be
                recalculated
        """
        # NOTE: If the kpi-calculator execution gets too long it's possible to create
        # new functions and split the execution into multiple parallel executions
        # processing a group of kpis divided into chunks
        kpi_codes = {k for p in raw_parameters for k in p.kpi_codes}
        logger.debug(f"Running kpi-calculator for kpis {kpi_codes}. (Fire-and-Forget)")
        if self._dry_run:
            return
        self._cognite_client.functions.call(
            external_id="kpi-calculator",
            wait=False,
            data={
                "kpi_codes": list(kpi_codes),
                "kpi_group": "",
            },
        )
