import uuid
from io import By<PERSON><PERSON>
from typing import Any, Dict, List
import time
import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeId
from gkpi_monitoring.config import DataTestConfig
from gkpi_monitoring.file_manager import FileManager
from gkpi_monitoring.models import DataError, DownloadFileResponse
from gkpi_monitoring.notification import NotificationService
from gkpi_monitoring.storage import Storage
from gkpi_monitoring.template import TemplateService
from gkpi_monitoring.raw_tables import stream_raw_to_parquet_and_list


class DataMonitorExecutor:
    def __init__(
        self,
        cognite_client: CogniteClient,
        config: DataTestConfig,
        file_manager: FileManager,
        notification_service: NotificationService,
        template_service: TemplateService,
    ):
        self._cognite_client = cognite_client
        self._config = config
        self._file_manager = file_manager
        self._notification_service = notification_service
        self._template_service = template_service
        self._storage = Storage(self._config.output_path)

    def execute(self) -> None:
        self._download_data()
        errors = self._validate_data()

        return self._notify(errors)

    def _notify(self, errors: dict[str, pd.DataFrame]) -> None:
        if not errors:
            return
        data_errors: list[DataError] = []
        for description, df in errors.items():
            file_response = self._file_manager.upload_file(str(uuid.uuid4()), df, "csv")
            res = self._cognite_client.files.retrieve(
                instance_id=NodeId(file_response.space, file_response.external_id)
            )
            assert res is not None
            data_errors.append(
                DataError(file=file_response, description=description, id=res.id)
            )

        message = self._template_service.render_teams_notification_data(data_errors)
        print(message)
        self._notification_service.send_teams_message(message)

    def _get_fdm_data(self, space: str, table: str) -> pd.DataFrame:
        views = self._cognite_client.data_modeling.views.retrieve(ids=(space, table))
        latest_view = max(
            (view for view in views),
            key=lambda view: view.last_updated_time,
            default=None,
        )

        if latest_view is None:
            raise ValueError(f"View {space}.{table} not found in Cognite Data Model")

        all_entries: List[Dict[str, Any]] = []
        cursor: str = None

        while True:
            payload = {
                "includeTyping": False,
                "sources": [
                    {
                        "source": {
                            "type": "view",
                            "space": latest_view.space,
                            "externalId": latest_view.external_id,
                            "version": latest_view.version
                        }
                    }
                ],
                "limit": 1000 
            }
            
            if cursor:
                payload["cursor"] = cursor

            try:
                print(f"(Cursor: {'Yes' if cursor else 'No'})")
                
                response = self._cognite_client.post(
                    url=f"/api/v1/projects/{self._cognite_client.config.project}/models/instances/list",
                    json=payload
                )
                
                response_json = response.json()

                items = response_json.get("items", [])
                
                for item_data in items:
                    entry: Dict[str, Any] = {
                        "externalId": item_data.get("externalId"),
                        "space": item_data.get("space"),
                    }
                    
                    properties = item_data.get("properties", {}).get(latest_view.space, {}).get(f"{latest_view.external_id}/{latest_view.version}", {})
                    if properties:
                        entry.update(properties)
                    
                    all_entries.append(entry)

                print(f"Received {len(items)} itens. Total: {len(all_entries)}.")

                cursor = response_json.get("nextCursor")

                if not cursor:
                    print("finished.")
                    break

            except Exception as e:
                print(f"Failure: {e}")
                print("Waiting 30 seconds to keep going...")
                time.sleep(30)
                continue
        
        if not all_entries:
            return pd.DataFrame()
            
        return pd.DataFrame(all_entries)

    from io import BytesIO

    def _download_big_raw_table(self, space: str, table: str) -> list[str]:
        return stream_raw_to_parquet_and_list(
            self._cognite_client,
            self._file_manager,
            space,
            table,
            page_limit=10_000,
            chunk_rows=500_000,
            max_retries=5,
        )

    def _get_raw_data(self, space: str, table: str) -> pd.DataFrame:
        base_name = f"CACHE-{space}-{table}"
        single = self._file_manager.download_file(base_name, "parquet")
        if single:
            return pd.read_parquet(BytesIO(single.data))

        list_cache = self._file_manager.download_file(f"{base_name}-parts-list", "csv")
        if list_cache:
            parts = pd.read_csv(BytesIO(list_cache.data))["file_name"].tolist()
            dfs = []
            for fname in parts:
                part_cache = self._file_manager.download_file(fname, "parquet")
                if not part_cache:
                    raise RuntimeError(f"Parte faltando no cache: {fname}")
                dfs.append(pd.read_parquet(BytesIO(part_cache.data)))
            return pd.concat(dfs, ignore_index=True)

        parts = stream_raw_to_parquet_and_list(
            self._cognite_client, self._file_manager, space, table
        )

        dfs = []
        for fname in parts:
            part_cache = self._file_manager.download_file(fname, "parquet")
            dfs.append(pd.read_parquet(BytesIO(part_cache.data)))
        return pd.concat(dfs, ignore_index=True)

    def _should_update_cache(
        self, space: str, table: str, file_cache: DownloadFileResponse | None
    ) -> bool:
        if file_cache is None:
            return True

        min_last_updated_time = int(file_cache.last_updated_time.timestamp() * 1000)

        existing = self._cognite_client.raw.rows.list(
            db_name=space,
            table_name=table,
            min_last_updated_time=min_last_updated_time,
            limit=1,
        )

        return len(existing) > 0

    def _validate_data(self) -> dict[str, pd.DataFrame]:
        print()
        print("Validating data...")
        errors: dict[str, pd.DataFrame] = {}
        for validation in self._config.validations:
            sql = validation.sql_file.read_text(encoding="utf-8")

            result = self._storage.query(sql)

            if not result.empty:
                print(f"Validation failed for {validation.description}")

                errors[validation.description] = result

            else:
                print(f"Validation passed for {validation.description}")

        return errors

    def _download_data(self) -> None:
        for data_source in self._config.data_sources:
            print(f"Processing data source: {data_source.alias}")
            df = (
                self._get_fdm_data(data_source.space, data_source.table)
                if data_source.origin == "fdm"
                else self._get_raw_data(data_source.space, data_source.table)
            )

            self._storage.insert_data(data_source.alias, df)
            print(f"Data saved for {data_source.alias}")
