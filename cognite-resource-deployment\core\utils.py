import json

import aiofiles
from croniter import croniter


async def load_json_async(file_path):
    async with aiofiles.open(file_path, mode="r", encoding="utf-8") as f:
        content = await f.read()
        return json.loads(content)


def has_duplicates(arr: list[str]):
    return len(arr) != len(set(arr))


def is_valid_cron(expression):
    try:
        croniter(expression)  # Try to parse the cron expression
        return True
    except Exception:
        return False
