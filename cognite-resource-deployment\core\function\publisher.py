import json
import logging
import os
import time

from cognite.client import CogniteClient

from core.cognite_utils import delete_function_if_exists, zip_and_upload_folder
from core.env_variables import EnvVariables

from .model import FunctionConfiguration

logger = logging.getLogger(__name__)


class FunctionPublisher:
    def __init__(
        self, env_vars: EnvVariables, client: CogniteClient, directory: str
    ) -> None:
        self._env_vars = env_vars
        self._client = client
        self._directory = directory

    def publish_function(self, function_directory_name: str):
        logger.info(f"Starting deployment for function f{function_directory_name}")

        logger.info("Finding function configuration")
        configs_dict = json.load(
            open(os.path.join(self._directory, "function_config.json"), "r")
        )
        function_config = FunctionConfiguration(**configs_dict)
        self._assert_pipeline_variables_are_loaded(function_config)

        logger.info(f"Deleting existent function {function_config.external_id}")
        delete_function_if_exists(self._client, function_config.external_id)

        logger.info(f"Creating Function from folder {self._directory}")
        file_id = zip_and_upload_folder(
            cognite_client=self._client,
            folder=self._directory,
            name=function_config.external_id,
            external_id=function_config.external_id,
            data_set_id=self._env_vars.cognite.data_set_id,
        )

        logger.info("Creating Function")
        func = self._client.functions.create(
            name=function_config.external_id,
            external_id=function_config.external_id,
            file_id=file_id,
            runtime=function_config.runtime,
            env_vars={
                v: os.getenv(v) or "MISSING" for v in function_config.pipeline_variables
            },
        )

        for s in function_config.schedules:
            logger.info(f"Creating schedule {s.schedule_name}-schedule")
            self._client.functions.schedules.create(
                name=s.schedule_name,
                cron_expression=s.cron,
                client_credentials={
                    "client_id": self._env_vars.auth.client_id,
                    "client_secret": self._env_vars.auth.secret,
                },
                function_id=func.id,
                data=s.data,
            )

        logger.info("Function created")
        self._status_check(func)

    def _assert_pipeline_variables_are_loaded(
        self, function_config: FunctionConfiguration
    ):
        for var in function_config.pipeline_variables:
            assert os.getenv(var), f"Variable {var} not found"

    def _status_check(self, function):
        logger.info("Verifying function deployment")
        start_time = time.perf_counter()
        time_elapsed = 0
        # Repeat until status is ready
        while function.status != "Ready":
            function.update()
            time_elapsed = int(time.perf_counter() - start_time)

            logger.info(function.status + f". Waiting for {time_elapsed} seconds")

            if function.status == "Failed":
                raise TimeoutError("Failed to deploy function")

            time.sleep(5)

        logger.info(
            f"Function is successfully deployed. Wait time: {time_elapsed} seconds."
        )
