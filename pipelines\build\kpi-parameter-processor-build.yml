trigger: none

variables:
  - group: kpi-dom
  - name: pythonVersion
    value: '3.11'
  - name: buildPath
    value: kpi-parameter-processor-function
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(PROD_SECRET)
    ${{ else }}:
      value: $(DSD_SECRET)
  - name: pythonEnv
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: qa
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: prod
    ${{ else }}:
      value: dev

pool:
  vmImage: ubuntu-latest

stages:
  - stage: Build
    jobs:
      - job: Build
        displayName: Lint & Test Python
        steps:
          - template: function-build-template.yml
