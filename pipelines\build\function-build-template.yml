steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: '$(pythonVersion)'
      addToPath: true

  - script: |
      python -m pip install --upgrade pip
      if [ -f requirements.txt ]; then
        pip install -r requirements.txt
      fi
      if [ -f requirements-dev.txt ]; then
        pip install -r requirements-dev.txt
      fi
    displayName: 'Install dependencies'
    workingDirectory: $(System.DefaultWorkingDirectory)/$(buildPath)
    condition: or(eq(variables.runLint, 'True'), eq(variables.runTests, 'True'))

  - script: |
      if [ -f ./.scripts/lint.sh ]; then
        source ./.scripts/lint.sh
      fi
    displayName: 'Linting'
    workingDirectory: $(System.DefaultWorkingDirectory)/$(buildPath)
    condition: eq(variables.runLint, 'True')

  - script: |
      if command -v pytest 2>&1 >/dev/null
      then
        pytest
      fi
    displayName: 'Running Tests'
    workingDirectory: $(System.DefaultWorkingDirectory)/$(buildPath)
    condition: eq(variables.runTests, 'True')
    env:
      RUN_INTEGRATION_TESTS: $(runIntegrationTests)
      AUTH_TOKEN_OVERRIDE: $(tokenOverride)
      AUTH_SECRET: ${{ variables.authSecret }}
      AUTH_CLIENT_ID: ${{ variables.authClientId }}
      PYTHON_ENV: ${{ variables.pythonEnv }}
