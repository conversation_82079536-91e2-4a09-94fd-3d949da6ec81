{"externalId": "wf-EXEC-COR-ALL-BPC-PLANT", "description": "Contextualize data for ExecuteKPIDOM - DollarPerKg, CostVariance, and VolumeVariance views", "version": "1_0_2", "schedules": [{"externalId": "wfs-EXEC-COR-ALL-BPC-PLANT-01", "cron": "0 12 11,16,21 * *", "data": null}], "tasks": [{"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::cln-utils", "type": "function", "dependsOn": [], "parameters": {"externalId": "cln-utils", "data": {"mode": "truncate_raw_table", "dbName": "EXEC-KPI", "tableName": "BPC-PLANT-ACTL-FRCT-RECENT"}}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-BPC-PLANT-RECENT", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::cln-utils"], "parameters": {"externalId": "tr-EXEC-COR-ALL-BPC-PLANT-RECENT", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-BPC-PLANT-LATEST-REPORT", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-BPC-PLANT-RECENT"], "parameters": {"externalId": "tr-EXEC-COR-ALL-BPC-PLANT-LATEST-REPORT", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-CVR", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-BPC-PLANT-LATEST-REPORT"], "parameters": {"externalId": "tr-EXEC-COR-ALL-CVR", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-VVR", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-BPC-PLANT-LATEST-REPORT"], "parameters": {"externalId": "tr-EXEC-COR-ALL-VVR", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-DKG", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-BPC-PLANT-LATEST-REPORT"], "parameters": {"externalId": "tr-EXEC-COR-ALL-DKG", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-CVR-DEL", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-CVR"], "parameters": {"externalId": "tr-EXEC-COR-ALL-CVR-DEL", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-VVR-DEL", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-VVR"], "parameters": {"externalId": "tr-EXEC-COR-ALL-VVR-DEL", "concurrencyPolicy": "restartAfterCurrent"}}, {"externalId": "wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-DKG-DEL", "type": "transformation", "dependsOn": ["wft-EXEC-COR-ALL-BPC-PLANT::tr-EXEC-COR-ALL-DKG"], "parameters": {"externalId": "tr-EXEC-COR-ALL-DKG-DEL", "concurrencyPolicy": "restartAfterCurrent"}}]}