import json

import requests

from gkpi_monitoring.config import NotificationConfig


class NotificationService:
    def __init__(self, config: NotificationConfig) -> None:
        self._config = config

    def send_teams_message(self, message: str) -> None:
        response = requests.post(
            self._config.teams_url,
            headers={"Content-Type": "application/json"},
            data=json.dumps({"text": message}),
        )

        response.raise_for_status()
