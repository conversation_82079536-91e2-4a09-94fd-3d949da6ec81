from pathlib import Path

import duckdb
import pandas as pd

FILE_NAME = "storage.db"


class Storage:
    def __init__(self, storage_path: Path):
        self._base_path = storage_path
        self._base_path.mkdir(parents=True, exist_ok=True)

        self._storage_path = self._base_path / FILE_NAME
        self._storage_path.unlink(missing_ok=True)

    def query(self, sql: str) -> pd.DataFrame:
        with duckdb.connect(database=self._storage_path, read_only=False) as con:
            return con.query(sql).to_df()

    def insert_data(self, table_name: str, df: pd.DataFrame) -> None:
        with duckdb.connect(database=self._storage_path, read_only=False) as con:
            sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} AS
            SELECT * from df
            """

            con.execute(sql)
