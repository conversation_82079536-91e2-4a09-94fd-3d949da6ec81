import logging

from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import CredentialProvider, OAuthClientCredentials, Token

from core.env_variables import EnvVariables

logging.basicConfig(
    format="[%(levelname)s] %(message)s",
    level=logging.INFO,
)

logger = logging.getLogger(__name__)


class CogniteClientFactory:
    @staticmethod
    def _create_credentials(env_variables: EnvVariables) -> CredentialProvider:
        auth_variables = env_variables.auth
        if auth_variables.token_override:
            logger.info("Using token override")
            return Token(auth_variables.token_override)

        return OAuthClientCredentials(
            token_url=auth_variables.token_uri,
            client_id=auth_variables.client_id,
            client_secret=auth_variables.secret,
            scopes=auth_variables.scopes,
        )

    @staticmethod
    def _create_client_config(env_variables: EnvVariables) -> ClientConfig:
        cognite_variables = env_variables.cognite
        return ClientConfig(
            client_name=cognite_variables.client_name,
            project=env_variables.cognite.project,
            credentials=CogniteClientFactory._create_credentials(env_variables),
            base_url=cognite_variables.base_uri,
            timeout=600,
        )

    @staticmethod
    def create(
        env_variables: EnvVariables,
    ) -> CogniteClient:
        logger.info(f"Creating client for project {env_variables.cognite.project}")
        return CogniteClient(
            config=CogniteClientFactory._create_client_config(env_variables)
        )
