from asyncio import sleep
from datetime import UTC, datetime

from gql import Client, gql


class GraphqlService:
    def __init__(self, client: Client):
        self._client = client
        self._session = None
        self._now = datetime.now(UTC)
        self._max_node_description_length = 100

    async def get_all_results_list(
        self,
        query: str,
        list_name: str,
        filter=None,
        first: int = 1_000,
    ):
        if filter is None:
            filter = {}
        session = await self._get_graphql_session()
        variables = {
            "filter": filter,
            "first": first,
            "after": None,
        }
        items = []
        retry_count = 0
        while True:
            result = []
            try:
                response = await session.execute(gql(query), variable_values=variables)
                result = response[list_name]
            except Exception as e:
                print(f"Error while fetching {list_name}: {e}")
                if retry_count > 5:
                    print(f"Max retries reached for {list_name}")
                    await self.cleanup()
                    raise e
                retry_count += 1
                await sleep(1)

                continue

            if result is None or "items" not in result:
                break

            items.extend(self._unwrap_query_result(item) for item in result["items"])

            page_info = result["pageInfo"]

            if not page_info["hasNextPage"] or page_info["endCursor"] is None:
                break

            variables["after"] = page_info["endCursor"]

        return items

    async def _get_graphql_session(self):
        if self._session is None:
            self._session = await self._client.connect_async(recconecting=True)

        return self._session

    async def cleanup(self):
        if self._session is not None:
            await self._client.close_async()

    def _unwrap_query_result(self, result: dict):
        if result is None:
            return None

        unwrapped_result = {}
        for key, value in result.items():
            if not isinstance(value, dict):
                unwrapped_result[key] = value
                continue

            if "items" in value:
                unwrapped_result[key] = [
                    self._unwrap_query_result(v) for v in value["items"] or []
                ]
                continue

            unwrapped_result[key] = self._unwrap_query_result(value)

        return unwrapped_result
