import logging
import os
import sys

from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables
from core.function.publisher import FunctionPublisher

logging.basicConfig(
    format="[%(levelname)s] %(message)s",
    level=logging.INFO,
)

logger = logging.getLogger(__name__)

env_variables = EnvVariables()
client = CogniteClientFactory.create(env_variables=env_variables)


def main():
    logger.info("Checking Function Name")
    argument_list = sys.argv
    if len(argument_list) > 2:
        raise ValueError("Only the function name is accepted as parameter.")
    if len(argument_list) == 1:
        raise ValueError("The function name must be provided.")

    function_directory: str = argument_list[1]
    current_directory = os.path.dirname(__file__)
    function_path = os.path.join(current_directory, "..", function_directory)
    publisher = FunctionPublisher(
        env_vars=env_variables,
        client=client,
        directory=function_path,
    )
    publisher.publish_function(function_directory)


if __name__ == "__main__":
    main()
