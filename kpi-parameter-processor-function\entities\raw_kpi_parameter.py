from datetime import datetime

import cronexpr

from entities.base import BaseCamelCaseModel


class RawKpiParameter(BaseCamelCaseModel):
    code: str
    kpi: str
    ingestion_transformation: str
    lowest_business_level: str
    is_protected: bool
    date_range_refresh: int
    ingestion_frequency: str

    @property
    def kpi_codes(self):
        return self.kpi.split(",")

    @property
    def external_id(self):
        return f"KPR-{self.code}"

    def is_due(self, start_time: datetime, end_time: datetime) -> bool:
        next_execution = cronexpr.next_fire(self.ingestion_frequency, start_time)
        return start_time <= next_execution <= end_time
