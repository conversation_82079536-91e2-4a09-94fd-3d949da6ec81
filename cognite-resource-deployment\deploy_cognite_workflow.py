import argparse
import async<PERSON>
import logging
import os

from cognite.client import CogniteClient

from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables
from core.workflow.publisher import WorkflowPublisher

logging.basicConfig(
    format="[%(levelname)s] %(message)s",
    level=logging.INFO,
)

logger = logging.getLogger(__name__)

env_variables = EnvVariables()
client: CogniteClient = CogniteClientFactory.create(env_variables=env_variables)


async def main():
    args = parse_args()

    current_directory = os.path.dirname(__file__)
    workflow_directory = os.path.join(current_directory, "..", "workflows")
    publisher = WorkflowPublisher(
        env_vars=env_variables,
        client=client,
        directory=workflow_directory,
        valid_prefixes=["KPI", "EXEC"],
    )
    force_deploy_ids = list(args.workflow_ids) if args.force else []
    ok, errors = await publisher.publish_workflows(
        args.dry_run,
        force_deploy_ids,
        args.skip_validation,
    )
    if not ok and errors:
        for e in errors:
            logger.error(e)
        exit(-1)


def parse_args():
    parser = argparse.ArgumentParser(
        description="Process command-line arguments.",
        epilog="""
Examples:
python -m deploy_cognite_workflow --dry-run
python -m deploy_cognite_workflow --dry-run --force tr-workflow-external-id-1 tr-workflow-external-id-2
python -m deploy_cognite_workflow
        """,
        formatter_class=argparse.RawTextHelpFormatter,
    )
    parser.add_argument(
        "--skip-validation",
        action="store_true",
        help="Run the program skipping the validation step",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run the program without making any changes.",
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force deploy the workflows.",
    )
    parser.add_argument(
        "workflow_ids",
        nargs="*",
        help="ExternalIDs of the workflows to force publish",
    )
    return parser.parse_args()


if __name__ == "__main__":
    asyncio.run(main())
