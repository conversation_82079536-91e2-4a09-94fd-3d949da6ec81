{"cells": [{"cell_type": "code", "execution_count": 1, "id": "80078e69", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["→ sys.path[0] agora é: c:\\Users\\<USER>\\Celanese\\KpiDOM\\gkpi-monitoring\n"]}], "source": ["import sys, os\n", "base = os.getcwd()\n", "parent = os.path.abspath(os.path.join(base, os.pardir))\n", "sys.path.insert(0, parent)\n", "print(\"→ sys.path[0] agora é:\", sys.path[0])"]}, {"cell_type": "code", "execution_count": null, "id": "7fd46d6e", "metadata": {}, "outputs": [], "source": ["# Cell 2: criação do client e do file_manager\n", "import collections.abc\n", "collections.MutableMapping = collections.abc.MutableMapping\n", "collections.Sequence = collections.abc.Sequence\n", "collections.Mapping = collections.abc.Mapping\n", "from cognite_client_factory import create_client\n", "from cognite_client_factory import create_client\n", "from gkpi_monitoring.file_manager import FileManager\n", "from cognite.client import CogniteClient\n", "import time, gc\n", "import pandas as pd\n", "from cognite.client.exceptions import CogniteAPIError\n", "from typing import List\n", "from io import BytesIO\n", "from http.client import RemoteDisconnected\n", "from requests.exceptions import ConnectionError\n", "\n", "CLIENT_ID = os.getenv(\"CLIENT_ID\")\n", "COGNITE_PROJECT = os.getenv(\"COGNITE_PROJECT\")\n", "SECRET = os.getenv(\"SECRET\")\n", "\n", "client = create_client(COGNITE_PROJECT, CLIENT_ID, SECRET)\n", "file_manager = FileManager(client, default_space=\"GKPI-COR-ALL-DAT\")"]}, {"cell_type": "code", "execution_count": null, "id": "3788397f", "metadata": {}, "outputs": [], "source": ["# Cell 3 — definição da função de streaming + parquet + upload\n", "\n", "def stream_raw_to_parquet_and_list(\n", "    client: CogniteClient,\n", "    file_manager: <PERSON><PERSON><PERSON><PERSON>,\n", "    space: str,\n", "    table: str,\n", "    page_limit: int = 10_000,\n", "    chunk_rows: int = 500_000,\n", "    max_retries: int = 5,\n", ") -> List[str]:\n", "    file_ids: List[str] = []\n", "    buffer: List[dict] = []\n", "    cursor: str | None = None\n", "    part = 0\n", "    page_count = 0\n", "\n", "    path = (\n", "        f\"/api/v1/projects/{client.config.project}/raw/dbs/{space}/tables/{table}/rows\"\n", "    )\n", "\n", "    while True:\n", "        page_count += 1\n", "        params = {\"limit\": page_limit}\n", "        if cursor:\n", "            params[\"cursor\"] = cursor\n", "\n", "        for attempt in range(max_retries + 1):\n", "            try:\n", "                resp = client.get(url=path, params=params)\n", "                break\n", "            except (CogniteAPIError, RemoteDisconnected, ConnectionError) as e:\n", "                if (isinstance(e, CogniteAPIError) and e.code == 429) or not isinstance(e, CogniteAPIError):\n", "                    wait = min(60, 2 ** (attempt + 1))\n", "                    print(f\"{type(e).__name__} → dormindo {wait}s (retry #{attempt+1})…\")\n", "                    time.sleep(wait)\n", "                else:\n", "                    raise\n", "\n", "        data = resp.json()\n", "        items = data.get(\"items\", [])\n", "        n = len(items)\n", "        buffer.extend(items)\n", "        cursor = data.get(\"nextCursor\")\n", "\n", "        total_read = part * chunk_rows + len(buffer)\n", "        if page_count % 20 == 0 or cursor is None:\n", "            print(\n", "                f\"➡️ Página #{page_count} trouxe {n:,} rows — total lido até agora: {total_read:,}\"\n", "            )\n", "\n", "        if len(buffer) >= chunk_rows or cursor is None:\n", "            print(\n", "                f\"💾 Gravando CACHE-{space}-{table}-part{part}.parquet ({len(buffer):,} rows)…\"\n", "            )\n", "            df = pd.json_normalize(buffer, sep=\"_\")\n", "            df.columns = [\n", "                col.split(\"columns_\")[-1] if col.startswith(\"columns_\") else col\n", "                for col in df.columns\n", "            ]\n", "            fname = f\"CACHE-{space}-{table}-part{part}.parquet\"\n", "            bio = BytesIO()\n", "            df.to_parquet(bio, index=False)\n", "            bio.seek(0)\n", "            file_manager.upload_file(fname, bio.read(), \"parquet\")\n", "            file_ids.append(fname)\n", "\n", "            part += 1\n", "            buffer.clear()\n", "            gc.collect()\n", "\n", "        if cursor is None:\n", "            break\n", "\n", "    list_name = f\"CACHE-{space}-{table}-parts-list.csv\"\n", "    parts_df = pd.DataFrame({\"file_name\": file_ids})\n", "    file_manager.upload_file(list_name, parts_df, \"csv\")\n", "    print(\n", "        f\"✅ Concluído: {len(file_ids)} parquets gerados + lista `{list_name}` enviada\"\n", "    )\n", "\n", "    return file_ids"]}, {"cell_type": "code", "execution_count": 4, "id": "e9b76e4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["➡️ Página #20 trouxe 1,267 rows — total lido até agora: 26,824\n", "➡️ Página #40 trouxe 1,328 rows — total lido até agora: 54,217\n", "➡️ Página #60 trouxe 1,371 rows — total lido até agora: 81,659\n", "➡️ Página #80 trouxe 1,425 rows — total lido até agora: 109,321\n", "➡️ Página #100 trouxe 1,479 rows — total lido até agora: 138,119\n", "➡️ Página #120 trouxe 1,431 rows — total lido até agora: 166,999\n", "➡️ Página #140 trouxe 1,440 rows — total lido até agora: 195,921\n", "➡️ Página #160 trouxe 1,431 rows — total lido até agora: 224,833\n", "➡️ Página #180 trouxe 1,468 rows — total lido até agora: 253,765\n", "➡️ Página #200 trouxe 1,471 rows — total lido até agora: 282,736\n", "➡️ Página #220 trouxe 1,395 rows — total lido até agora: 311,616\n", "➡️ Página #240 trouxe 1,370 rows — total lido até agora: 339,210\n", "➡️ Página #260 trouxe 1,355 rows — total lido até agora: 366,392\n", "➡️ Página #280 trouxe 1,317 rows — total lido até agora: 393,154\n", "➡️ Página #300 trouxe 1,358 rows — total lido até agora: 419,987\n", "➡️ Página #320 trouxe 1,318 rows — total lido até agora: 446,384\n", "➡️ Página #340 trouxe 1,385 rows — total lido até agora: 473,954\n", "➡️ Página #360 trouxe 1,383 rows — total lido até agora: 501,310\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part0.parquet (501,310 rows)…\n", "➡️ Página #380 trouxe 1,370 rows — total lido até agora: 527,362\n", "➡️ Página #400 trouxe 1,462 rows — total lido até agora: 556,079\n", "➡️ Página #420 trouxe 1,469 rows — total lido até agora: 584,985\n", "➡️ Página #440 trouxe 1,447 rows — total lido até agora: 613,922\n", "➡️ Página #460 trouxe 1,458 rows — total lido até agora: 642,848\n", "➡️ Página #480 trouxe 1,455 rows — total lido até agora: 671,693\n", "➡️ Página #500 trouxe 1,434 rows — total lido até agora: 700,670\n", "➡️ Página #520 trouxe 1,472 rows — total lido até agora: 729,620\n", "➡️ Página #540 trouxe 1,395 rows — total lido até agora: 757,593\n", "➡️ Página #560 trouxe 1,348 rows — total lido até agora: 785,048\n", "➡️ Página #580 trouxe 1,327 rows — total lido até agora: 811,795\n", "➡️ Página #600 trouxe 1,349 rows — total lido até agora: 838,542\n", "➡️ Página #620 trouxe 1,344 rows — total lido até agora: 865,214\n", "➡️ Página #640 trouxe 1,351 rows — total lido até agora: 892,522\n", "➡️ Página #660 trouxe 1,349 rows — total lido até agora: 919,914\n", "➡️ Página #680 trouxe 1,378 rows — total lido até agora: 947,278\n", "➡️ Página #700 trouxe 1,446 rows — total lido até agora: 975,528\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part1.parquet (500,138 rows)…\n", "➡️ Página #720 trouxe 1,459 rows — total lido até agora: 1,004,389\n", "➡️ Página #740 trouxe 1,459 rows — total lido até agora: 1,033,281\n", "➡️ Página #760 trouxe 1,433 rows — total lido até agora: 1,062,174\n", "➡️ Página #780 trouxe 1,443 rows — total lido até agora: 1,091,090\n", "➡️ Página #800 trouxe 1,441 rows — total lido até agora: 1,120,137\n", "➡️ Página #820 trouxe 1,437 rows — total lido até agora: 1,149,126\n", "➡️ Página #840 trouxe 1,396 rows — total lido até agora: 1,177,568\n", "➡️ Página #860 trouxe 1,392 rows — total lido até agora: 1,205,144\n", "➡️ Página #880 trouxe 1,349 rows — total lido até agora: 1,231,995\n", "➡️ Página #900 trouxe 1,332 rows — total lido até agora: 1,258,775\n", "➡️ Página #920 trouxe 1,328 rows — total lido até agora: 1,285,537\n", "➡️ Página #940 trouxe 1,263 rows — total lido até agora: 1,312,160\n", "➡️ Página #960 trouxe 1,365 rows — total lido até agora: 1,339,610\n", "➡️ Página #980 trouxe 1,378 rows — total lido até agora: 1,366,922\n", "➡️ Página #1000 trouxe 1,461 rows — total lido até agora: 1,394,860\n", "➡️ Página #1020 trouxe 1,461 rows — total lido até agora: 1,423,693\n", "➡️ Página #1040 trouxe 1,441 rows — total lido até agora: 1,452,642\n", "➡️ Página #1060 trouxe 1,467 rows — total lido até agora: 1,481,670\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part2.parquet (500,488 rows)…\n", "➡️ Página #1080 trouxe 1,434 rows — total lido até agora: 1,510,120\n", "➡️ Página #1100 trouxe 1,467 rows — total lido até agora: 1,539,105\n", "➡️ Página #1120 trouxe 1,434 rows — total lido até agora: 1,567,996\n", "➡️ Página #1140 trouxe 1,368 rows — total lido até agora: 1,596,758\n", "➡️ Página #1160 trouxe 1,359 rows — total lido até agora: 1,624,341\n", "➡️ Página #1180 trouxe 1,331 rows — total lido até agora: 1,651,545\n", "➡️ Página #1200 trouxe 1,359 rows — total lido até agora: 1,678,383\n", "➡️ Página #1220 trouxe 1,319 rows — total lido até agora: 1,705,146\n", "➡️ Página #1240 trouxe 1,455 rows — total lido até agora: 1,732,094\n", "➡️ Página #1260 trouxe 1,365 rows — total lido até agora: 1,759,560\n", "➡️ Página #1280 trouxe 1,367 rows — total lido até agora: 1,786,924\n", "➡️ Página #1300 trouxe 1,470 rows — total lido até agora: 1,814,373\n", "➡️ Página #1320 trouxe 1,453 rows — total lido até agora: 1,843,134\n", "➡️ Página #1340 trouxe 1,438 rows — total lido até agora: 1,872,017\n", "➡️ Página #1360 trouxe 1,430 rows — total lido até agora: 1,900,961\n", "➡️ Página #1380 trouxe 1,439 rows — total lido até agora: 1,929,880\n", "➡️ Página #1400 trouxe 1,451 rows — total lido até agora: 1,958,844\n", "➡️ Página #1420 trouxe 1,434 rows — total lido até agora: 1,987,753\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part3.parquet (500,813 rows)…\n", "➡️ Página #1440 trouxe 1,440 rows — total lido até agora: 2,015,931\n", "➡️ Página #1460 trouxe 1,365 rows — total lido até agora: 2,043,825\n", "➡️ Página #1480 trouxe 1,338 rows — total lido até agora: 2,071,246\n", "➡️ Página #1500 trouxe 1,326 rows — total lido até agora: 2,098,061\n", "➡️ Página #1520 trouxe 1,349 rows — total lido até agora: 2,124,837\n", "➡️ Página #1540 trouxe 1,464 rows — total lido até agora: 2,151,640\n", "➡️ Página #1560 trouxe 1,387 rows — total lido até agora: 2,179,060\n", "➡️ Página #1580 trouxe 1,378 rows — total lido até agora: 2,206,603\n", "➡️ Página #1600 trouxe 1,361 rows — total lido até agora: 2,233,913\n", "➡️ Página #1620 trouxe 1,430 rows — total lido até agora: 2,262,293\n", "➡️ Página #1640 trouxe 1,450 rows — total lido até agora: 2,291,163\n", "➡️ Página #1660 trouxe 1,434 rows — total lido até agora: 2,319,997\n", "➡️ Página #1680 trouxe 1,433 rows — total lido até agora: 2,348,966\n", "➡️ Página #1700 trouxe 1,443 rows — total lido até agora: 2,377,878\n", "➡️ Página #1720 trouxe 1,460 rows — total lido até agora: 2,406,810\n", "➡️ Página #1740 trouxe 1,459 rows — total lido até agora: 2,435,825\n", "➡️ Página #1760 trouxe 1,388 rows — total lido até agora: 2,464,221\n", "➡️ Página #1780 trouxe 1,351 rows — total lido até agora: 2,491,830\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part4.parquet (501,310 rows)…\n", "➡️ Página #1800 trouxe 1,331 rows — total lido até agora: 2,517,379\n", "➡️ Página #1820 trouxe 1,359 rows — total lido até agora: 2,544,167\n", "➡️ Página #1840 trouxe 1,344 rows — total lido até agora: 2,570,961\n", "➡️ Página #1860 trouxe 1,273 rows — total lido até agora: 2,598,036\n", "➡️ Página #1880 trouxe 1,390 rows — total lido até agora: 2,625,425\n", "➡️ Página #1900 trouxe 1,350 rows — total lido até agora: 2,652,796\n", "➡️ Página #1920 trouxe 1,471 rows — total lido até agora: 2,680,646\n", "➡️ Página #1940 trouxe 1,453 rows — total lido até agora: 2,709,372\n", "➡️ Página #1960 trouxe 1,462 rows — total lido até agora: 2,738,268\n", "➡️ Página #1980 trouxe 1,451 rows — total lido até agora: 2,767,170\n", "➡️ Página #2000 trouxe 1,449 rows — total lido até agora: 2,796,113\n", "➡️ Página #2020 trouxe 1,446 rows — total lido até agora: 2,825,173\n", "➡️ Página #2040 trouxe 1,471 rows — total lido até agora: 2,854,100\n", "➡️ Página #2060 trouxe 1,372 rows — total lido até agora: 2,882,927\n", "➡️ Página #2080 trouxe 1,389 rows — total lido até agora: 2,910,511\n", "➡️ Página #2100 trouxe 1,325 rows — total lido até agora: 2,937,688\n", "➡️ Página #2120 trouxe 1,350 rows — total lido até agora: 2,964,452\n", "➡️ Página #2140 trouxe 1,331 rows — total lido até agora: 2,991,267\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part5.parquet (500,623 rows)…\n", "➡️ Página #2160 trouxe 1,454 rows — total lido até agora: 3,017,338\n", "➡️ Página #2180 trouxe 1,373 rows — total lido até agora: 3,044,849\n", "➡️ Página #2200 trouxe 1,351 rows — total lido até agora: 3,072,232\n", "➡️ Página #2220 trouxe 1,395 rows — total lido até agora: 3,099,635\n", "➡️ Página #2240 trouxe 1,455 rows — total lido até agora: 3,128,494\n", "➡️ Página #2260 trouxe 1,463 rows — total lido até agora: 3,157,341\n", "➡️ Página #2280 trouxe 1,413 rows — total lido até agora: 3,186,259\n", "➡️ Página #2300 trouxe 1,419 rows — total lido até agora: 3,215,141\n", "➡️ Página #2320 trouxe 1,464 rows — total lido até agora: 3,244,138\n", "➡️ Página #2340 trouxe 1,463 rows — total lido até agora: 3,273,135\n", "➡️ Página #2360 trouxe 1,467 rows — total lido até agora: 3,302,047\n", "➡️ Página #2380 trouxe 1,401 rows — total lido até agora: 3,329,996\n", "➡️ Página #2400 trouxe 1,349 rows — total lido até agora: 3,357,442\n", "➡️ Página #2420 trouxe 1,323 rows — total lido até agora: 3,384,198\n", "➡️ Página #2440 trouxe 1,335 rows — total lido até agora: 3,411,044\n", "➡️ Página #2460 trouxe 1,246 rows — total lido até agora: 3,437,529\n", "➡️ Página #2480 trouxe 1,372 rows — total lido até agora: 3,464,938\n", "➡️ Página #2500 trouxe 1,384 rows — total lido até agora: 3,492,445\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part6.parquet (500,624 rows)…\n", "➡️ Página #2520 trouxe 1,360 rows — total lido até agora: 3,519,150\n", "➡️ Página #2540 trouxe 1,448 rows — total lido até agora: 3,547,510\n", "➡️ Página #2560 trouxe 1,458 rows — total lido até agora: 3,576,351\n", "➡️ Página #2580 trouxe 1,443 rows — total lido até agora: 3,605,254\n", "➡️ Página #2600 trouxe 1,431 rows — total lido até agora: 3,634,205\n", "➡️ Página #2620 trouxe 1,425 rows — total lido até agora: 3,663,106\n", "➡️ Página #2640 trouxe 1,492 rows — total lido até agora: 3,692,079\n", "➡️ Página #2660 trouxe 1,480 rows — total lido até agora: 3,721,041\n", "➡️ Página #2680 trouxe 1,358 rows — total lido até agora: 3,749,454\n", "➡️ Página #2700 trouxe 1,357 rows — total lido até agora: 3,777,078\n", "➡️ Página #2720 trouxe 1,331 rows — total lido até agora: 3,803,929\n", "➡️ Página #2740 trouxe 1,349 rows — total lido até agora: 3,830,684\n", "➡️ Página #2760 trouxe 1,341 rows — total lido até agora: 3,857,466\n", "➡️ Página #2780 trouxe 1,249 rows — total lido até agora: 3,884,278\n", "➡️ Página #2800 trouxe 1,367 rows — total lido até agora: 3,911,815\n", "➡️ Página #2820 trouxe 1,361 rows — total lido até agora: 3,939,254\n", "➡️ Página #2840 trouxe 1,456 rows — total lido até agora: 3,967,203\n", "➡️ Página #2860 trouxe 1,455 rows — total lido até agora: 3,995,957\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part7.parquet (500,269 rows)…\n", "➡️ Página #2880 trouxe 1,443 rows — total lido até agora: 4,024,636\n", "➡️ Página #2900 trouxe 1,454 rows — total lido até agora: 4,053,515\n", "➡️ Página #2920 trouxe 1,425 rows — total lido até agora: 4,082,408\n", "➡️ Página #2940 trouxe 1,462 rows — total lido até agora: 4,111,340\n", "➡️ Página #2960 trouxe 1,460 rows — total lido até agora: 4,140,303\n", "➡️ Página #2980 trouxe 1,372 rows — total lido até agora: 4,169,157\n", "➡️ Página #3000 trouxe 1,395 rows — total lido até agora: 4,196,751\n", "➡️ Página #3020 trouxe 1,322 rows — total lido até agora: 4,223,905\n", "➡️ Página #3040 trouxe 1,334 rows — total lido até agora: 4,250,706\n", "➡️ Página #3060 trouxe 1,357 rows — total lido até agora: 4,277,469\n", "➡️ Página #3067 trouxe 1,297 rows — total lido até agora: 4,286,817\n", "💾 Gravando CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part8.parquet (286,817 rows)…\n", "✅ Concluído: 9 parquets gerados + lista `CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-parts-list.csv` enviada\n", "Arquivos criados:\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part0.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part1.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part2.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part3.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part4.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part5.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part6.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part7.parquet\n", " • CACHE-SAP-COR-BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A-part8.parquet\n"]}], "source": ["space = \"SAP-COR\"\n", "table = \"BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A\"\n", "\n", "parts = stream_raw_to_parquet_and_list(\n", "    client,\n", "    file_manager,\n", "    space,\n", "    table,\n", "    page_limit=10_000,\n", "    chunk_rows=500_000,\n", "    max_retries=5,\n", ")\n", "\n", "print(\"Arquivos criados:\")\n", "for f in parts:\n", "    print(\" •\", f)"]}, {"cell_type": "code", "execution_count": 5, "id": "e9956bd8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Arquivo unificado gravado em: C:\\Users\\<USER>\\Celanese\\KpiDOM\\gkpi-monitoring\\merged-ZMINMB52-FG-A.parquet\n"]}], "source": ["import pandas as pd\n", "import pyarrow as pa\n", "import pyarrow.parquet as pq\n", "from io import BytesIO\n", "\n", "def merge_parquet_parts_to_disk(\n", "    space: str,\n", "    table: str,\n", "    file_manager,\n", "    output_path: str,\n", ") -> str:\n", "\n", "    # 1) tenta as duas variáveis (com e sem .csv)\n", "    candidates = [\n", "        f\"CACHE-{space}-{table}-parts-list\",\n", "        f\"CACHE-{space}-{table}-parts-list.csv\",\n", "    ]\n", "    csv_resp = None\n", "    for name in candidates:\n", "        csv_resp = file_manager.download_file(name, \"csv\")\n", "        if csv_resp:\n", "            break\n", "    if not csv_resp:\n", "        raise FileNotFoundError(\n", "            f\"Não encontrei nenhum destes:\\n\" +\n", "            \"\\n\".join(f\"  • {c}.csv\" for c in candidates)\n", "        )\n", "\n", "    part_names = pd.read_csv(BytesIO(csv_resp.data))[\"file_name\"].tolist()\n", "    if not part_names:\n", "        raise ValueError(\"Lista de partes está vazia\")\n", "\n", "    # 2) recolhe todos os schemas\n", "    schemas = []\n", "    for fname in part_names:\n", "        resp = file_manager.download_file(fname, \"parquet\")\n", "        if resp is None:\n", "            raise FileNotFoundError(f\"Não encontrei a parte {fname}\")\n", "        tbl = pq.read_table(BytesIO(resp.data))\n", "        schemas.append(tbl.schema)\n", "    unified_schema = pa.unify_schemas(schemas)\n", "\n", "    # 3) abre o writer direto no disco\n", "    writer = pq.ParquetWriter(output_path, unified_schema)\n", "\n", "    # 4) para cada parte: al<PERSON><PERSON> e escreve\n", "    for fname in part_names:\n", "        resp = file_manager.download_file(fname, \"parquet\")\n", "        tbl = pq.read_table(BytesIO(resp.data))\n", "\n", "        cols = []\n", "        for field in unified_schema:\n", "            if field.name in tbl.schema.names:\n", "                col = tbl.column(field.name)\n", "                # se o tipo não bater, faz cast\n", "                if not col.type.equals(field.type):\n", "                    col = col.cast(field.type)\n", "            else:\n", "                # coluna inexistente nessa parte → preenche com nulos\n", "                col = pa.array([None] * tbl.num_rows, type=field.type)\n", "            cols.append(col)\n", "\n", "        aligned = pa.Table.from_arrays(cols, names=unified_schema.names)\n", "        writer.write_table(aligned)\n", "\n", "    writer.close()\n", "    return output_path\n", "\n", "\n", "# Exemplo de uso:\n", "space = \"SAP-COR\"\n", "table = \"BLKSTK-BLKSTKREPORT-ZMINMB52-FG-A\"\n", "output_file = r\"C:\\Users\\<USER>\\Celanese\\KpiDOM\\gkpi-monitoring\\merged-ZMINMB52-FG-A.parquet\"\n", "\n", "\n", "merged_path = merge_parquet_parts_to_disk(space, table, file_manager, output_file)\n", "print(\"Arquivo unificado gravado em:\", merged_path)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "3e708c98", "metadata": {}, "outputs": [], "source": ["path = r\"C:\\Users\\<USER>\\Celanese\\KpiDOM\\gkpi-monitoring\\merged-ZMINMB52-FG-A.parquet\"\n", "\n", "# carrega tudo num DataFrame\n", "df_raw = pd.read_parquet(path)"]}, {"cell_type": "code", "execution_count": 7, "id": "d43455a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully retrieved data model(s) ('EDG-COR-ALL-DMD', 'QualityDOM', '3_0_3')\n", "Cleaned temporary directory C:\\Users\\<USER>\\AppData\\Local\\Temp\\pygen\\EDG-COR-ALL-DMD_QualityDOM_3_0_3\n", "Name collision detected in ViewId(space='EDG-COR-ALL-DMD', external_id='CoordinateReferenceSystem', version='8332777d9a6f22'): 'id'. An underscore will be added to the 'id' to avoid name collision.\n", "Writing SDK to C:\\Users\\<USER>\\AppData\\Local\\Temp\\pygen\\EDG-COR-ALL-DMD_QualityDOM_3_0_3\\quality_dom\n", "Done!\n", "Added C:\\Users\\<USER>\\AppData\\Local\\Temp\\pygen\\EDG-COR-ALL-DMD_QualityDOM_3_0_3 to sys.path to enable import\n", "Imported quality_dom\n", "You can now use the generated SDK in the current Python session.\n", "The data classes are available by importing, for example, `from quality_dom.data_classes import BlockStockWrite`\n"]}], "source": ["from cognite.pygen import generate_sdk_notebook\n", "\n", "space = \"EDG-COR-ALL-DMD\"\n", "data_model_external_id = \"QualityDOM\"\n", "data_model_version = \"3_0_3\"\n", "data_model_id = (space, data_model_external_id, data_model_version)\n", "quality_client = generate_sdk_notebook(data_model_id, client)"]}, {"cell_type": "code", "execution_count": 8, "id": "c7269e9c", "metadata": {}, "outputs": [], "source": ["block_stock_df = quality_client.block_stock.list(limit=None).to_pandas()"]}, {"cell_type": "code", "execution_count": 64, "id": "57db3e5d", "metadata": {}, "outputs": [], "source": ["cols = [\"external_id\", \"Age\", \"Date into b\", \"Date of Pr\", \"report_date\", \"posting_date\", \"is_blocked\", \"comments\"]"]}, {"cell_type": "code", "execution_count": 76, "id": "3cdb8901", "metadata": {}, "outputs": [], "source": ["# ─── Bloco 1: Preparar a raw ─────────────────────────────────────────────────\n", "# renomear para lowercase\n", "df_raw.rename(columns={'Report_date': 'report_date'}, inplace=True)\n", "\n", "# criar chave external_id\n", "df_raw['external_id'] = (\n", "    'BS-FG-' + df_raw['Plnt'].astype(str) + '-' +\n", "    df_raw['Material'].astype(str) + '-' +\n", "    df_raw['Batch'].astype(str) + '-' +\n", "    df_raw['SLoc'].astype(str)\n", ")\n", "\n", "# aplicar ROW_NUMBER() = 1 para pegar apenas a última extração de cada grupo\n", "df_raw['rank'] = (\n", "    df_raw.sort_values('Age', ascending=False)\n", "          .groupby(['Plnt','<PERSON>','Batch','<PERSON><PERSON>'])\n", "          .cumcount() + 1\n", ")\n", "df_raw = df_raw[df_raw['rank'] == 1]\n"]}, {"cell_type": "code", "execution_count": 77, "id": "98eb44d0", "metadata": {}, "outputs": [], "source": ["# ─── Bloco 2: Converter tipos na raw ─────────────────────────────────────────\n", "# 2.1) report_date → datetime sem fuso\n", "df_raw['report_date'] = (\n", "    pd.to_datetime(df_raw['report_date'], utc=True, errors='coerce')\n", "      .dt.tz_localize(None)\n", ")\n", "\n", "# 2.2) Age → numérico\n", "df_raw['Age'] = pd.to_numeric(df_raw['Age'], errors='coerce')\n", "\n"]}, {"cell_type": "code", "execution_count": 78, "id": "f4d01275", "metadata": {}, "outputs": [], "source": ["# ─── <PERSON><PERSON><PERSON> da coluna is_blocked para bool ──────────────────────────────────────\n", "# Trata valores nulos como False e converte tudo para bool\n", "block_stock_df[\"is_blocked\"] = (\n", "    block_stock_df[\"is_blocked\"]\n", "      .fillna(False)          # Se tiver None, torna False\n", "      .astype(bool)           # Converte para dtype bool\n", ")\n"]}, {"cell_type": "code", "execution_count": 79, "id": "3b098aa1", "metadata": {}, "outputs": [], "source": ["# ─── Bloco 3 (a<PERSON><PERSON><PERSON>): <PERSON>ltrar e preparar o Data Model ──────────────────\n", "from datetime import date\n", "\n", "limite = pd.Timestamp('2025-06-30', tz='UTC')\n", "\n", "filtered_dm = (\n", "    block_stock_df[\n", "        (~block_stock_df['is_blocked']) &\n", "        (block_stock_df['report_date'] < limite) &\n", "        (block_stock_df['block_stock_type'] == 'FGBS')\n", "    ][\n", "        ['external_id', 'posting_date', 'report_date', 'is_blocked']  # <— incluímos is_blocked\n", "    ]\n", "    .copy()\n", ")\n", "\n", "# normalize posting_date e report_date\n", "for c in ['posting_date','report_date']:\n", "    filtered_dm[c] = (\n", "        pd.to_datetime(filtered_dm[c], utc=True, errors='coerce')\n", "          .dt.tz_localize(None)\n", "    )\n", "\n"]}, {"cell_type": "code", "execution_count": 80, "id": "86ef49ea", "metadata": {}, "outputs": [], "source": ["# ─── Bloco 4: Selecionar última raw por external_id ──────────────────────────\n", "df_sub = df_raw[df_raw['external_id'].isin(filtered_dm['external_id'])]\n", "df_latest_raw = (\n", "    df_sub\n", "      .sort_values('report_date', ascending=False)\n", "      .drop_duplicates(subset=['external_id'], keep='first')\n", "    [['external_id','Age']]\n", ")"]}, {"cell_type": "code", "execution_count": 84, "id": "37c3500b", "metadata": {}, "outputs": [], "source": ["# ─── Bloco 5 (a<PERSON><PERSON><PERSON>): <PERSON><PERSON> e c<PERSON>l<PERSON>lo de datas ─────────────────────────\n", "# o filtered_dm agora já tem is_blocked, então o merge leva essa coluna para o df_calc\n", "df_calc = filtered_dm.merge(df_latest_raw, on='external_id', how='left')\n", "\n", "df_calc['calculated_report_date'] = (\n", "    df_calc['posting_date'] + pd.to_timedelta(df_calc['Age'], unit='D')\n", ")\n", "\n", "df_calc['diff_days'] = (\n", "    df_calc['report_date'].dt.normalize()\n", "  - df_calc['calculated_report_date'].dt.normalize()\n", ").dt.days"]}, {"cell_type": "code", "execution_count": 82, "id": "2096330c", "metadata": {}, "outputs": [], "source": ["# ─── Bloco 6: Métricas finais excluindo o dia 16/06 ─────────────────────────\n", "from datetime import date\n", "\n", "# 6.1) Mascara para excluir 2025-06-16\n", "mask_not16   = df_calc['report_date'].dt.date != date(2025,6,16)\n", "df_ok        = df_calc[mask_not16]\n", "\n", "total        = len(df_calc)\n", "total_exc16  = len(df_ok)\n", "\n", "exact_exc16     = (df_ok['diff_days'] == 0).sum()\n", "pct_exact_exc16 = exact_exc16 / total_exc16 * 100\n", "\n", "no_age          = df_calc['Age'].isna().sum()\n", "pct_no_age      = no_age / total * 100\n", "\n", "no_posting      = df_calc['posting_date'].isna().sum()\n", "pct_no_posting  = no_posting / total * 100\n", "\n", "within3_exc16     = (df_ok['diff_days'].abs() <= 3).sum()\n", "pct_within3_exc16 = within3_exc16 / total_exc16 * 100"]}, {"cell_type": "code", "execution_count": 83, "id": "faa9a4ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                Description   Value\n", "0                     Total rows FGBS on DM   25143\n", "1                   Rows FGBS on 2025-06-16    4332\n", "2          Cases diff_days = 0 (exc. 16/06)   15720\n", "3            Pct diff_days = 0 (exc. 16/06)  75.54%\n", "4                                    No Age     120\n", "5                                Pct No Age   0.48%\n", "6                           No posting_date     734\n", "7                       Pct No posting_date   2.92%\n", "8  Cases |diff_days| <= 3 days (exc. 16/06)   17376\n", "9    Pct |diff_days| <= 3 days (exc. 16/06)  83.49%\n"]}], "source": ["# ─── Bloco 7: <PERSON><PERSON> o DataFrame de report ──────────────────────────────────\n", "report = pd.DataFrame({\n", "    'Description': [\n", "        'Total rows FGBS on DM',\n", "        'Rows FGBS on 2025-06-16',\n", "        'Cases diff_days = 0 (exc. 16/06)',\n", "        'Pct diff_days = 0 (exc. 16/06)',\n", "        'No Age',\n", "        'Pct No Age',\n", "        'No posting_date',\n", "        'Pct No posting_date',\n", "        'Cases |diff_days| <= 3 days (exc. 16/06)',\n", "        'Pct |diff_days| <= 3 days (exc. 16/06)'\n", "    ],\n", "    'Value': [\n", "        total,\n", "        (df_calc['report_date'].dt.date == date(2025,6,16)).sum(),\n", "        exact_exc16,\n", "        f\"{pct_exact_exc16:.2f}%\",\n", "        no_age,\n", "        f\"{pct_no_age:.2f}%\",\n", "        no_posting,\n", "        f\"{pct_no_posting:.2f}%\",\n", "        within3_exc16,\n", "        f\"{pct_within3_exc16:.2f}%\"\n", "    ]\n", "})\n", "\n", "print(report)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}