import os
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import cast
from zipfile import ZipF<PERSON>

from cognite.client import CogniteClient


def _sanitize_filename(filename: str) -> str:
    # Forwardslash, '/', is not allowed in file names:
    return filename.replace("/", "-")


def zip_and_upload_folder(
    cognite_client: CogniteClient,
    folder: str,
    name: str,
    data_set_id: int,
    external_id: str | None = None,
) -> int:
    name = _sanitize_filename(name)
    current_dir = os.getcwd()
    os.chdir(folder)
    # List of directories to exclude
    exclude_dirs = {
        ".venv",
        "__pycache__",
        ".git",
        ".vscode",
        ".ruff_cache",
        ".pytest_cache",
    }
    exclude_files = [".env.local"]
    try:
        with TemporaryDirectory() as tmpdir:
            zip_path = Path(tmpdir, "function.zip")
            with ZipFile(zip_path, "w") as zf:
                for root, dirs, files in os.walk("."):
                    # Remove unwanted directories from traversal
                    dirs[:] = [d for d in dirs if d not in exclude_dirs]
                    zf.write(root)
                    for filename in files:
                        if filename not in exclude_files:
                            zf.write(Path(root, filename))

            overwrite = True if external_id else False
            file = cognite_client.files.upload_bytes(
                zip_path.read_bytes(),
                name=f"{name}.zip",
                external_id=external_id,
                overwrite=overwrite,
                data_set_id=data_set_id,
            )
            return cast(int, file.id)
    finally:
        os.chdir(current_dir)


def delete_function_if_exists(client: CogniteClient, external_id: str):
    try:
        client.functions.delete(external_id=external_id)
    except Exception as _:
        ...
