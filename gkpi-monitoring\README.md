# 📊 Global KPI Monitor & Data Quality Validator

This project aims to **monitor the infrastructure** (workflows, transformations, and functions) related to the **Global KPI** system, as well as to **validate data quality**. It is designed to be **highly configurable and generic**, allowing easy adaptation to other business domains through a single file: `config.toml`.

---

## 🌐 Overview

- 🔧 **Infrastructure Monitoring**: Observes transformations, functions, and workflows running in the CDF platform.
- 📈 **Data Quality Validation**: Runs business rules through SQL scripts to ensure data integrity and consistency.
- 🔔 **Automated Notifications**: Sends alerts to Microsoft Teams when infrastructure issues or data quality errors are detected.
- ⚙️ **Centralized Configuration**: All logic and settings are defined in the `config.toml` file to promote reuse and simplicity.

---

## 🧱 Setup Structure

```
.
├── config.toml                          # Main configuration file
├── validations/
│   ├── validate_mapping_impact_subtype.sql
│   ├── validate_mapping_impact_levels.sql
│   ├── validate_incident_cority_sites.sql
│   ├── validate_incident_enablon_sites.sql
│   └── validate_incident_category_count.sql
├── templates/
│   ├── infra_notification_issues.txt   # Notification template for infra issues
│   └── data_notification_issues.txt    # Notification template for data issues
└── temp/                               # Output folder for validation results
```

---

## ⚙️ `[resources]` Configuration

The `[resources]` section defines which infrastructure components will be monitored:

```toml
[resources]
functions = ["gkpi-kpi-calculator"]
transformations = [
  "RAW:REF-ALL-REFDATA:KPR-KPIParameter:ingestionTransformation"
]
workflows = [
  "wf-EXEC-COR-ALL-BPC-PLANT",
  "wf-EXEC-COR-ALL-PCM",
  "wf-EXEC-COR-ALL-PRD",
  "wf-KPI-COR-ALL-MAIN"
]
```

### 🧠 Note

To enable **dynamic monitoring**, you can point to a column in a `raw` table using this pattern:

```
RAW:{space}:{table}:{column}
```

The system will automatically fetch the values from that column and monitor the resolved items.

---

## 🔔 `[notification]` Configuration

The `[notification]` section defines where and how alerts should be sent:

```toml
[notification]
teams_url = "<teams webhook url>"
teams_notification_infra_template = "templates/infra_notification_issues.txt"
teams_notification_data_template = "templates/data_notification_issues.txt"
```

- `teams_url`: Webhook URL for Microsoft Teams where alerts will be sent.
- `teams_notification_infra_template`: Message template for infrastructure-related alerts.
- `teams_notification_data_template`: Message template for data-related alerts.

---

## 📂 `[data_test]` Configuration

The `[data_test]` section defines which datasets will be used and what validations will be performed.

### General Parameters

| Parameter            | Description                                                 |
| -------------------- | ----------------------------------------------------------- |
| `output_path`        | Path where validation results will be stored (`temp`)       |
| `force_update_cache` | If `true`, forces cache invalidation and reloads fresh data |

### Data Sources (SAMPLES)

| Alias                 | Space              | Table                     | Origin |
| --------------------- | ------------------ | ------------------------- | ------ |
| `incident_sp_list`    | INC-STEWARDSHIP-SP | INC-INCIDENT-SP-LIST      | raw    |
| `minor_inj_master`    | INC-STEWARDSHIP-SP | INC-MINOR-INJ-MASTER-FILE | raw    |
| `enablon_mapping`     | REF-ALL-REFDATA    | EnablonMappingCategories  | raw    |
| `sites`               | REF-ALL-REFDATA    | STS-Sites                 | raw    |
| `units`               | REF-ALL-REFDATA    | UNT-Units (Innoart)       | raw    |
| `locations`           | REF-ALL-REFDATA    | LOC-Locations             | raw    |
| `safety_incident`     | CRTY-COR           | INC-SAFETYINCIDENT        | raw    |
| `incident`            | ENA-COR            | INC-Incident              | raw    |
| `swp_incident_impact` | SWP-COR-ALL-DMD    | SwpIncidentImpact         | fdm    |

---

## ✅ SQL Validations (SAMPLES)

The following validations are executed using `.sql` files under the `validations/` folder:

| SQL File                               | Description                                                            |
| -------------------------------------- | ---------------------------------------------------------------------- |
| `validate_mapping_impact_subtype.sql`  | Validates whether impact subtypes are mapped correctly                 |
| `validate_mapping_impact_levels.sql`   | Checks if impact levels follow expected rules                          |
| `validate_incident_cority_sites.sql`   | Validates site mapping in Cority incident records                      |
| `validate_incident_enablon_sites.sql`  | Validates site mapping in Enablon incident records                     |
| `validate_incident_category_count.sql` | Validates category counts for incidents based on business expectations |

---

## ▶️ How to Run

1. Update the `config.toml` file according to the components and validations you need.
2. Install uv (https://docs.astral.sh/uv/getting-started/installation/)
3. Run the infra validation pipeline with the following command:
   ```bash
   uv run main.py monitor-infra
   ```
4. Run the data validation pipeline with the following command:
   ```bash
   uv run main.py monitor-data
   ```
5. Notifications will be automatically sent to the configured Teams channel if any issue is found.

---

## 📌 Requirements

- Python 3.11+
- Install uv (https://docs.astral.sh/uv/getting-started/installation/)

---
