[project]
name = "gkpi-monitoring"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "cognite-sdk>=7.74.0",
    "duckdb>=1.2.1",
    "jinja2>=3.1.6",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "pyarrow>=19.0.1",
    "pydantic>=2.11.1",
    "python-dotenv>=1.1.0",
    "requests>=2.32.3",
    "toml>=0.10.2",
    "typer>=0.15.2",
]

[tool.uv]
dev-dependencies = [
    "mypy>=1.15.0",
    "pandas-stubs>=2.2.3.250308",
    "ruff>=0.11.2",
    "types-requests>=2.32.0.20250328",
    "types-toml>=0.10.8.20240310",
]


[tool.mypy]
strict = true
exclude = ["venv", ".venv"]

[tool.ruff]
target-version = "py311"


[tool.ruff.lint]
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # pyflakes
    "I",      # isort
    "B",      # flake8-bugbear
    "C4",     # flake8-comprehensions
    "UP",     # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "W191", # indentation contains tabs
    "B904", # Allow raising exceptions without from e, for HTTPException
]
