import os
from functools import lru_cache
from pathlib import Path
from typing import List, Literal

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

env_files: list[Path | str] = [
    ".env",
    f".env.{os.getenv('PYTHON_ENV', 'local').lower()}",
]


class AuthVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_files,
        env_prefix="auth_",
        extra="ignore",
    )

    client_id: str
    tenant_id: str
    secret: str
    scopes_str: str = Field(alias="auth_scopes")
    token_uri: str
    token_override: str | None = None

    @property
    def scopes(self) -> List[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_files,
        env_prefix="cognite_",
        extra="ignore",
    )
    base_uri: str
    client_name: str
    data_set_id: int
    project: Literal["celanese-dev", "celanese-stg", "celanese"]


class EnvVariables:
    def __init__(self) -> None:
        self.auth = AuthVariables()  # type: ignore
        self.cognite = CogniteVariables()  # type: ignore


@lru_cache
def get_env_variables():
    return EnvVariables()
