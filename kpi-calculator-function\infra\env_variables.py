import os
from functools import lru_cache
from pathlib import Path

from cognite.client.data_classes.data_modeling import DataModelIdentifier
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

env_files: list[Path | str] = [
    ".env",
    f".env.{os.getenv('PYTHON_ENV', 'local').lower()}",
]


class AuthVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_files,
        env_prefix="auth_",
        extra="ignore",
        env_file_encoding="utf-8",
    )
    tenant_id: str = ""
    client_id: str = ""
    secret: str = ""
    scopes_str: str = Field(alias="auth_scopes", default="")
    token_uri: str = ""
    token_override: str | None = None
    teams_notification_webhook_url: str | None = None

    @property
    def scopes(self) -> list[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_files,
        env_prefix="cognite_",
        extra="ignore",
        env_file_encoding="utf-8",
    )
    base_uri: str = ""
    client_name: str = ""
    project: str = "celanese-dev"

    asset_hierarchy_model_space: str = ""

    kpi_data_model_space: str = ""
    kpi_data_model_name: str = ""
    kpi_data_model_version: str = ""

    @property
    def kpi_data_model_id(self) -> DataModelIdentifier:
        return (
            self.kpi_data_model_space,
            self.kpi_data_model_name,
            self.kpi_data_model_version,
        )

    @property
    def graphql_uri_kpidom(self) -> str:
        return f"{self.base_uri}/api/v1/projects/{self.project}/userapis/spaces/{self.kpi_data_model_space}/datamodels/{self.kpi_data_model_name}/versions/{self.kpi_data_model_version}/graphql"


class EnvVariables:
    def __init__(self) -> None:
        self.cognite = CogniteVariables()
        self.auth = AuthVariables()


@lru_cache
def get_env_variables():
    return EnvVariables()
