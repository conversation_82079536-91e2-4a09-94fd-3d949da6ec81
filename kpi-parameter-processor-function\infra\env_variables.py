import os
from functools import lru_cache
from pathlib import Path

from cognite.client.data_classes.data_modeling import DataModelIdentifier
from loguru import logger
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

env_files: list[Path | str] = [
    ".env",
    f".env.{os.getenv('PYTHON_ENV', 'local').lower()}",
]


class AuthVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_files,
        env_prefix="auth_",
        extra="ignore",
        env_file_encoding="utf-8",
    )
    tenant_id: str = ""
    client_id: str = ""
    secret: str = ""
    scopes_str: str = Field(alias="auth_scopes", default="")
    token_uri: str = ""
    token_override: str | None = None
    teams_notification_webhook_url: str | None = None

    @property
    def scopes(self) -> list[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_files,
        env_prefix="cognite_",
        extra="ignore",
        env_file_encoding="utf-8",
    )
    base_uri: str = ""
    client_name: str = ""
    project: str = "celanese-dev"
    raw_ref_data_db: str = ""
    raw_ref_kpi_param_table: str = ""

    transformation_wait_time: int = 300
    function_interval_in_hours: int = 1

    kpi_data_model_space: str = ""
    kpi_data_model_name: str = ""
    kpi_data_model_version: str = ""

    @property
    def kpi_data_model_id(self) -> DataModelIdentifier:
        return (
            self.kpi_data_model_space,
            self.kpi_data_model_name,
            self.kpi_data_model_version,
        )


class EnvVariables:
    def __init__(self) -> None:
        self.cognite = CogniteVariables()
        self.auth = AuthVariables()


@lru_cache
def get_env_variables():
    files_read = [f for f in env_files if os.path.exists(f)]
    logger.debug(f"Sourcing files {files_read}")
    return EnvVariables()
