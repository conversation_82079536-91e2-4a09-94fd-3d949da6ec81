WITH spIncLastReportDate AS (
    SELECT
        SPLIT_PART(sp."Report Date", 'T', 1) AS last_report_date
    FROM
        incident_sp_list AS sp
    ORDER BY
        sp."Report Date" DESC
    LIMIT
        1
), minorInjLastReportDate AS (
    select
        split_part(minorInj."Report_Date", 'T', 1) as last_report_date
    from
        minor_inj_master minorInj
    ORDER BY
        minorInj."Report_Date" DESC
    limit
        1
)
select
    minorInj."System Number" AS system_number,
    incRAW."Reference" as enablonSiteCode,
    minorInjDate.last_report_date as report_date
from
    minor_inj_master AS minorInj
    JOIN minorInjLastReportDate minorInjDate ON minorInjDate.last_report_date = split_part(minorInj."Report_Date", 'T', 1)
    JOIN incident incRAW ON incRAW."Id" = CAST(
        SPLIT_PART(minorInj."System Number", '.', -1) AS INT
    )
    LEFT JOIN sites stsRAW ON (
        (
            instr(incRAW."Reference", 'Site47') = 0
            AND stsRAW."EnablonSiteCode" = SPLIT_PART(incRAW."Reference", '.', 1)
        )
        OR (
            instr(incRAW."Reference", 'Site47') > 0
            AND instr(
                stsRAW."EnablonMajorProjectSiteCodes",
                CONCAT(
                    SPLIT_PART(incRAW."Reference", '.', 1),
                    '.',
                    SPLIT_PART(incRAW."Reference", '.', 2)
                )
            ) > 0
        )
    )
where
    instr(minorInj."System Number", 'Site') > 0
    AND minorInj."Injury Classification" NOT IN ('Report Only', 'Near Miss')
    AND stsRAW."ReportingSiteCode" is null
UNION
select
    spInc."System Number" AS system_number,
    incRAW."Reference" as enablonSiteCode,
    spIncInjDate.last_report_date as report_date
from
    incident_sp_list AS spInc
    JOIN spIncLastReportDate spIncInjDate ON spIncInjDate.last_report_date = split_part(spInc."Report Date", 'T', 1)
    JOIN incident incRAW ON incRAW."Id" = CAST(
        SPLIT_PART(spInc."System Number", '.', -1) AS INT
    )
    LEFT JOIN sites stsRAW ON (
        (
            instr(incRAW."Reference", 'Site47') = 0
            AND stsRAW."EnablonSiteCode" = SPLIT_PART(incRAW."Reference", '.', 1)
        )
        OR (
            instr(incRAW."Reference", 'Site47') > 0
            AND instr(
                stsRAW."EnablonMajorProjectSiteCodes",
                CONCAT(
                    SPLIT_PART(incRAW."Reference", '.', 1),
                    '.',
                    SPLIT_PART(incRAW."Reference", '.', 2)
                )
            ) > 0
        )
    )
where
    instr(spInc."System Number", 'Site') > 0
    AND stsRAW."ReportingSiteCode" is null