import asyncio
import logging
from pathlib import Path

from cognite.client import CogniteClient
from cognite.client.data_classes import (
    ClientCredentials,
    WorkflowTriggerList,
    WorkflowVersionList,
)
from cognite.client.data_classes.workflows import (
    WorkflowUpsert,
)

from ..env_variables import EnvVariables
from ..utils import load_json_async
from .models import WorkflowConfig, validate_workflow_external_id

logger = logging.getLogger(__name__)


class WorkflowPublisher:
    def __init__(
        self,
        env_vars: EnvVariables,
        client: CogniteClient,
        directory: str,
        valid_prefixes: list[str],
    ) -> None:
        self._env_vars = env_vars
        self._client = client
        self._directory = directory
        self._valid_prefixes = valid_prefixes
        self._all_triggers: WorkflowTriggerList = []  # type: ignore

    async def publish_workflows(
        self,
        dry_run: bool,
        force_deploy_ids: list[str],
        skip_validation: bool = False,
    ):
        workflow_configs = await self._get_workflow_config_from_dir_async()
        existing_versions = self._get_existing_version()
        existing_workflows = {v.workflow_external_id for v in existing_versions}
        workflows_to_publish = self._get_workflows_to_publish(
            workflow_configs, existing_versions, force_deploy_ids
        )
        workflows_to_delete = self._get_deleted_workflows(
            workflow_configs, existing_workflows
        )
        if not workflows_to_publish and not workflows_to_delete:
            logger.info("Nothing to publish")
            return True, None
        else:
            logger.info(
                f"Worklows to be published: {[w.external_id for w in workflows_to_publish]}",
            )
            logger.info(
                f"Worklows to be deleted: {workflows_to_delete}",
            )

        if not skip_validation:
            logger.info("Validating workflows configurations")
            validation_results = [
                w.validate_workflow(existing_versions) for w in workflows_to_publish
            ]
            error_messages = [e for v in validation_results if not v[0] for e in v[1]]
            logger.info("Validating workflow dependencies")
            error_messages.extend(
                self._validate_workflow_dependencies(workflows_to_publish)
            )
            if error_messages:
                return False, error_messages
            logger.info("Workflows validated successfuly!")

        if dry_run:
            return True, None

        for w in workflows_to_publish:
            self._upsert_workflow(w)

        self._delete_missing_triggers(workflows_to_publish)

        if workflows_to_delete:
            self._delete_workflows(workflows_to_delete)
        return True, None

    async def _get_workflow_config_from_dir_async(
        self,
    ):
        logger.info("Loading workflows configs from directory")
        files = [file for file in Path(self._directory).rglob("*.json")]
        assert not any(
            f
            for f in files
            if not validate_workflow_external_id(f.name, self._valid_prefixes)
        ), "Invalid prefix"
        json_files = await asyncio.gather(*[load_json_async(f) for f in files])
        return [WorkflowConfig(**j) for j in json_files]

    def _get_existing_version(self):
        logger.info("Loading existing versions")
        return self._client.workflows.versions.list(limit=-1)

    def _get_workflows_to_publish(
        self,
        workflow_configs: list[WorkflowConfig],
        existing_versions: WorkflowVersionList,
        force_deploy_ids: list[str],
    ):
        existing_ids = existing_versions.as_ids()
        return [
            w
            for w in workflow_configs
            if w.version_id not in existing_ids or w.external_id in force_deploy_ids
        ]

    def _get_deleted_workflows(
        self,
        workflow_configs: list[WorkflowConfig],
        existing_workflows_ids: set[str],
    ):
        config_ids = [w.external_id for w in workflow_configs]
        return [
            id
            for id in existing_workflows_ids
            if validate_workflow_external_id(id, self._valid_prefixes)
            and id not in config_ids
        ]

    def _upsert_workflow(
        self,
        workflow: WorkflowConfig,
    ):
        assert workflow.tasks, "Workflow must have tasks"
        logger.info(f"Upserting workflow {workflow.external_id}")
        self._client.workflows.upsert(
            WorkflowUpsert(
                external_id=workflow.external_id,
                description=workflow.description,
                data_set_id=self._env_vars.cognite.data_set_id,
            )
        )
        logger.info(
            f"\tUpserting workflow version {workflow.external_id}, version: {workflow.version}"
        )
        self._client.workflows.versions.upsert(workflow.to_version_upsert())
        if workflow.schedules and self._env_vars.auth.secret:
            logger.info(f"\tAdding workflow schedule {workflow.external_id}")
            triggers = workflow.to_triggers_upsert()
            for trigger in triggers:
                self._client.workflows.triggers.upsert(
                    workflow_trigger=trigger,
                    client_credentials=ClientCredentials(
                        client_id=self._env_vars.auth.client_id,
                        client_secret=self._env_vars.auth.secret,
                    ),
                )

    def _delete_workflows(self, workflow_ids: list[str]):
        logger.info(f"Deleting workflows: {workflow_ids}")
        all_triggers = self._get_triggers()
        workflow_triggers = [
            t.external_id
            for t in all_triggers
            if t.workflow_external_id in workflow_ids
        ]
        self._client.workflows.triggers.delete(external_id=workflow_triggers)
        self._client.workflows.delete(
            external_id=workflow_ids,
            ignore_unknown_ids=True,
        )

    def _delete_missing_triggers(self, workflows_to_publish: list[WorkflowConfig]):
        workflow_ids = {w.external_id for w in workflows_to_publish}
        schedule_ids = {
            s.external_id for w in workflows_to_publish for s in w.schedules or []
        }
        all_triggers = self._get_triggers()
        all_wf_trigger_ids = {
            t.external_id
            for t in all_triggers
            if t.workflow_external_id in workflow_ids
        }
        triggers_to_delete = all_wf_trigger_ids - schedule_ids
        logger.info(f"Deleting triggers: {triggers_to_delete}")
        self._client.workflows.triggers.delete(external_id=list(triggers_to_delete))

    def _get_triggers(self):
        if self._all_triggers:
            return self._all_triggers
        self._all_triggers = self._client.workflows.triggers.list(limit=-1)
        return self._all_triggers

    def _validate_workflow_dependencies(self, workflows: list[WorkflowConfig]):
        transf_map = {w.external_id: w.get_all_transformations_ids() for w in workflows}
        transformation_ids = {
            id for transformations in transf_map.values() for id in transformations
        }
        existing_transformations = (
            self._client.transformations.retrieve_multiple(
                external_ids=list(transformation_ids), ignore_unknown_ids=True
            )
            if transformation_ids
            else []
        )

        func_map = {w.external_id: w.get_all_function_ids() for w in workflows}
        function_ids = {id for functions in func_map.values() for id in functions}
        existing_functions = (
            self._client.functions.retrieve_multiple(
                external_ids=list(function_ids), ignore_unknown_ids=True
            )
            if function_ids
            else []
        )

        validation_errors = []

        existing_transformations_ids = {t.external_id for t in existing_transformations}
        missing_transf = transformation_ids - existing_transformations_ids
        if missing_transf:
            for missing_f in missing_transf:
                dependant_workflows = self._get_transformation_dependent_workflows(
                    transf_map, missing_f
                )
                validation_errors.append(
                    f"Transformation '{missing_f}' required by the workflows '{dependant_workflows}' was not found"
                )

        existing_functions_ids = {f.external_id for f in existing_functions}
        missing_function = function_ids - existing_functions_ids
        if missing_function:
            for missing_f in missing_function:
                dependant_workflows = self._get_function_dependent_workflows(
                    func_map, missing_f
                )
                validation_errors.append(
                    f"Function '{missing_f}' required by workflows {dependant_workflows} was not found"
                )

        return validation_errors

    def _get_transformation_dependent_workflows(
        self, workflows_to_transformations: dict[str, list[str]], transformation: str
    ):
        return [
            workflow_id
            for workflow_id, transf in workflows_to_transformations
            if transformation in transf
        ]

    def _get_function_dependent_workflows(
        self, workflows_to_functions: dict[str, list[str]], function: str
    ):
        return [
            workflow_id
            for workflow_id, functions in workflows_to_functions.items()
            if function in functions
        ]
