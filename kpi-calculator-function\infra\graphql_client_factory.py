import os
import sys

from cognite.client import CogniteClient
from gql import Client
from gql.transport.aiohttp import AIOHTTPTransport

from .env_variables import CogniteVariables

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class GraphqlClientFactory:
    @staticmethod
    def create(cognite_client: CogniteClient, cognite_env_variables: CogniteVariables):
        key, value = cognite_client.config.credentials.authorization_header()
        headers = {key: value}

        transport = AIOHTTPTransport(
            url=cognite_env_variables.graphql_uri_kpidom,
            headers=headers,
        )

        return Client(transport=transport, fetch_schema_from_transport=False)
