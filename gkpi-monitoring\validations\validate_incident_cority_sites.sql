WITH assetHierarchyLocationTree AS (
    SELECT
        sts."ReportingSiteCode" AS site_code,
        NULL AS unit_code,
        NULL AS location_code
    FROM
        sites AS sts
    UNION
    SELECT
        unt."ReportingSiteCode" AS site_code,
        unt."ReportingUnitCode" AS unit_code,
        NULL AS location_code
    FROM
        units AS unt
    UNION
    SELECT
        loc."ReportingSite" AS site_code,
        loc."ReportingUnit" AS unit_code,
        loc."Name" AS location_code
    FROM
        locations AS loc
),
spIncLastReportDate AS (
    SELECT
        SPLIT_PART(sp."Report Date", 'T', 1) AS last_report_date
    FROM
        incident_sp_list AS sp
    ORDER BY
        sp."Report Date" DESC
    LIMIT
        1
), minorInjLastReportDate AS (
    select
        split_part(minorInj."Report_Date", 'T', 1) as last_report_date
    from
        minor_inj_master minorInj
    ORDER BY
        minorInj."Report_Date" DESC
    limit
        1
)
SELECT
    DISTINCT sp."System Number" AS system_number,
    tree.site_code,
    last_date.last_report_date as report_date
FROM
    incident_sp_list AS sp
    JOIN spIncLastReportDate AS last_date ON SPLIT_PART(sp."Report Date", 'T', 1) = last_date.last_report_date
    LEFT JOIN safety_incident AS cority ON cority."generalIncidentNumber" = REGEXP_REPLACE(sp."System Number", '[^0-9]', '', 'g')
    LEFT JOIN assetHierarchyLocationTree AS tree ON (
        (
            tree.site_code = cority."building_code"
            AND tree.unit_code IS NULL
            AND tree.location_code IS NULL
        )
        OR (
            tree.unit_code = cority."building_code"
            AND tree.location_code IS NULL
        )
        OR (tree.location_code = cority."building_code")
    )
    LEFT JOIN sites AS sites ON (
        sites."ReportingSiteCode" = sp."Site Code"
    )
WHERE
    INSTR(sp."System Number", 'Site') = 0
    AND tree.site_code IS NULL
    AND sites."ReportingSiteCode" IS NULL
UNION
SELECT
    sp."System Number" AS system_number,
    tree.site_code,
    last_date.last_report_date as report_date
FROM
    minor_inj_master AS sp
    JOIN minorInjLastReportDate AS last_date ON SPLIT_PART(sp."Report_Date", 'T', 1) = last_date.last_report_date
    LEFT JOIN safety_incident AS cority ON cority."generalIncidentNumber" = REGEXP_REPLACE(sp."System Number", '[^0-9]', '', 'g')
    LEFT JOIN assetHierarchyLocationTree AS tree ON (
        (
            tree.site_code = cority."building_code"
            AND tree.unit_code IS NULL
            AND tree.location_code IS NULL
        )
        OR (
            tree.unit_code = cority."building_code"
            AND tree.location_code IS NULL
        )
        OR (tree.location_code = cority."building_code")
    )
    LEFT JOIN sites AS sites ON (
        INSTR(
            sp."System Number",
            sites."ReportingSiteCode"
        ) > 0
    )
WHERE
    INSTR(sp."System Number", 'Site') = 0
    AND sp."Injury Classification" NOT IN ('Report Only', 'Near Miss')
    AND tree.site_code IS NULL
    AND sites."ReportingSiteCode" IS NULL