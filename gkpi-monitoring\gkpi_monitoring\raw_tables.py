import sys, os

base = os.getcwd()
parent = os.path.abspath(os.path.join(base, os.pardir))
sys.path.insert(0, parent)

import collections.abc

collections.MutableMapping = collections.abc.MutableMapping
collections.Sequence = collections.abc.Sequence
collections.Mapping = collections.abc.Mapping
from gkpi_monitoring.cognite_client_factory import create_client
from gkpi_monitoring.cognite_client_factory import create_client
from gkpi_monitoring.file_manager import FileManager
from cognite.client import CogniteClient
import time, gc
import pandas as pd
from cognite.client.exceptions import CogniteAPIError
from typing import List
from io import BytesIO
import os

CLIENT_ID = os.getenv("CLIENT_ID")
COGNITE_PROJECT = os.getenv("COGNITE_PROJECT")
SECRET = os.getenv("SECRET")

client = create_client(COGNITE_PROJECT, CLIENT_ID, SECRET)
file_manager = FileManager(client, default_space="GKPI-COR-ALL-DAT")


def stream_raw_to_parquet_and_list(
    client: CogniteClient,
    file_manager: FileManager,
    space: str,
    table: str,
    page_limit: int = 10_000,
    chunk_rows: int = 250_000,
    max_retries: int = 5,
) -> List[str]:
    file_ids: List[str] = []
    buffer: List[dict] = []
    cursor: str | None = None
    part = 0
    page_count = 0

    path = (
        f"/api/v1/projects/{client.config.project}/raw/dbs/{space}/tables/{table}/rows"
    )

    while True:
        page_count += 1
        params = {"limit": page_limit}
        if cursor:
            params["cursor"] = cursor

        for attempt in range(max_retries + 1):
            try:
                resp = client.get(url=path, params=params)
                break
            except CogniteAPIError as e:
                if e.code == 429 and attempt < max_retries:
                    wait = min(60, 2 ** (attempt + 1))
                    print(f"429 → dormindo {wait}s (retry #{attempt+1})…")
                    time.sleep(wait)
                else:
                    raise

        data = resp.json()
        items = data.get("items", [])
        n = len(items)
        buffer.extend(items)
        cursor = data.get("nextCursor")

        total_read = part * chunk_rows + len(buffer)
        if page_count % 20 == 0 or cursor is None:
            print(
                f"➡️ Página #{page_count} trouxe {n:,} rows — total lido até agora: {total_read:,}"
            )

        if len(buffer) >= chunk_rows or cursor is None:
            print(
                f"💾 Gravando CACHE-{space}-{table}-part{part}.parquet ({len(buffer):,} rows)…"
            )
            df = pd.json_normalize(buffer, sep="_")
            df.columns = [
                col.split("columns_")[-1] if col.startswith("columns_") else col
                for col in df.columns
            ]
            fname = f"CACHE-{space}-{table}-part{part}.parquet"
            bio = BytesIO()
            df.to_parquet(bio, index=False)
            bio.seek(0)
            file_manager.upload_file(fname, bio.read(), "parquet")
            file_ids.append(fname)

            part += 1
            buffer.clear()
            gc.collect()

        if cursor is None:
            break

    list_name = f"CACHE-{space}-{table}-parts-list.csv"
    parts_df = pd.DataFrame({"file_name": file_ids})
    file_manager.upload_file(list_name, parts_df, "csv")
    print(
        f"✅ Concluído: {len(file_ids)} parquets gerados + lista `{list_name}` enviada"
    )

    return file_ids
