from industrial_model import ViewInstance
from pydantic import Field


class CoreImpactCategory(ViewInstance):
    name: str


class Kpi(ViewInstance):
    code: str
    name: str
    formula: str
    category: CoreImpactCategory


class KpiParameter(ViewInstance):
    code: str
    name: str


class KpiTimeGranularity(ViewInstance):
    code: str
    name: str


class KpiParameterValue(ViewInstance):
    values: list[str] = Field(default_factory=list)
    parameter: KpiParameter


class KpiResult(ViewInstance):
    kpi: Kpi
    time_granularity: KpiTimeGranularity
    parameter_values: list[KpiParameterValue] = Field(default_factory=list)
    value: str
