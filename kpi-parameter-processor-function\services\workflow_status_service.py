from datetime import UTC, datetime, timedelta

from cognite.client import <PERSON>gniteC<PERSON>
from cognite.client.data_classes import WorkflowVersion, WorkflowVersionId

from entities.teams_notification_input import TeamsNotificationInput
from infra.ms_teams_client import MsTeamsClient


class WorkflowStatusService:
    def __init__(self, client: CogniteClient, teams: MsTeamsClient) -> None:
        self._cognite_client = client
        self._teams = teams

    async def notify_previous_failed_execution_async(
        self, interval_in_hours: int
    ) -> None:
        """
        Search for errros in the last workflow execution and if an error is found
        a new message is sent to the Teams' channel
        """
        detailed_log = self._get_detailed_execution_log(interval_in_hours)
        if not detailed_log:
            return
        logs_from_transformations = [
            f"""
<b>ExternalId:</b> {t.external_id}<br>
<b>Status:</b> {t.status}<br>
<b>Reason for incompletion:</b> {t.reason_for_incompletion}<br>
<hr>
            """
            for t in detailed_log.executed_tasks
            if t.task_type != "function" and t.status != "completed"
        ]
        if not logs_from_transformations:
            return
        await self._teams.send_message_async(
            TeamsNotificationInput(
                title="KPI Workflow Error",
                text="<br>".join(logs_from_transformations),
                activity_messages=[],
            )
        )

    def _get_detailed_execution_log(self, interval_in_hours: int):
        last_execution_timestamp = (
            datetime.now(UTC) - timedelta(hours=interval_in_hours)
        ).timestamp() * 1000
        last_failed_execution = self._cognite_client.workflows.executions.list(
            workflow_version_ids=self._get_last_workflow_version(),
            created_time_start=int(last_execution_timestamp),
            statuses=["failed", "terminated", "timed_out"],
            limit=1,
        )
        if not last_failed_execution:
            return
        return self._cognite_client.workflows.executions.retrieve_detailed(
            id=last_failed_execution[0].id
        )

    def _get_last_workflow_version(self) -> WorkflowVersionId:
        versions = self._cognite_client.workflows.versions.list("wf-KPI-COR-ALL-MAIN")
        ordered_versions = sorted(versions, key=lambda x: int(x.version), reverse=True)
        last_version = next(iter(ordered_versions))
        assert isinstance(last_version, WorkflowVersion)
        return last_version.as_id()
