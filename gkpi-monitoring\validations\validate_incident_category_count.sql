WITH spIncLastReportDate AS (
    SELECT
        SPLIT_PART(sp."Report Date", ':', 1) AS last_report_date
    FROM
        incident_sp_list AS sp
    ORDER BY
        sp."Report Date" DESC
    LIMIT
        1
), minorInjLastReportDate AS (
    select
        split_part(minorInj."Report_Date", ':', 1) as last_report_date
    from
        minor_inj_master minorInj
    ORDER BY
        minorInj."Report_Date" DESC
    limit
        1
), categoryCountRaw AS (
    SELECT
        'PPS-T1-T2' as category,
        count(*) as total
    FROM
        incident_sp_list AS sp
        JOIN spIncLastReportDate spIncInjDate ON spIncInjDate.last_report_date = split_part(sp."Report Date", ':', 1)
    WHERE
        sp."People Safety Classification" <> 'Not Applicable'
    UNION
    SELECT
        'PCS-T1-T2-T3' as category,
        count(*) as total
    FROM
        incident_sp_list AS sp
        JOIN spIncLastReportDate spIncInjDate ON spIncInjDate.last_report_date = split_part(sp."Report Date", ':', 1)
    WHERE
        sp."API 754" <> 'Not Applicable'
    UNION
    SELECT
        'FIR-T1-T2-T3' as category,
        count(*) as total
    FROM
        incident_sp_list AS sp
        JOIN spIncLastReportDate spIncInjDate ON spIncInjDate.last_report_date = split_part(sp."Report Date", ':', 1)
    WHERE
        sp."Fire" <> 'Not Applicable'
    UNION
    SELECT
        'ENV-T1-T2-T3' as category,
        count(*) as total
    FROM
        incident_sp_list AS sp
        JOIN spIncLastReportDate spIncInjDate ON spIncInjDate.last_report_date = split_part(sp."Report Date", ':', 1)
    WHERE
        sp."Environment CE" <> 'Not Applicable'
    UNION
    SELECT
        'HPE' as category,
        count(*) as total
    FROM
        incident_sp_list AS sp
        JOIN spIncLastReportDate spIncInjDate ON spIncInjDate.last_report_date = split_part(sp."Report Date", ':', 1)
    WHERE
        sp."HPE" = 'Yes'
    UNION
    SELECT
        'PPS-T3' as category,
        count(*) as total
    FROM
        minor_inj_master minorInj
        JOIN minorInjLastReportDate minorInjDate ON minorInjDate.last_report_date = split_part(minorInj."Report_Date", ':', 1)
    WHERE
        minorInj."Injury Classification" NOT IN ('Report Only', 'Near Miss')
),
categoryCountDM AS (
    SELECT
        'PPS-T1-T2' as category,
        count(*) as total
    FROM
        swp_incident_impact
    WHERE
        category.externalId IN ('ITL-PPS-T1', 'ITL-PPS-T2')
    UNION
    SELECT
        'PCS-T1-T2-T3' as category,
        count(*) as total
    FROM
        swp_incident_impact
    WHERE
        category.externalId IN ('ITL-PCS-T1', 'ITL-PCS-T2', 'ITL-PCS-T3')
    UNION
    SELECT
        'FIR-T1-T2-T3' as category,
        count(*) as total
    FROM
        swp_incident_impact
    WHERE
        category.externalId IN ('ITL-FIR-T1', 'ITL-FIR-T2', 'ITL-FIR-T3')
    UNION
    SELECT
        'ENV-T1-T2-T3' as category,
        count(*) as total
    FROM
        swp_incident_impact
    WHERE
        category.externalId IN ('ITL-ENV-T1', 'ITL-ENV-T2', 'ITL-ENV-T3')
    UNION
    SELECT
        'HPE' as category,
        count(*) as total
    FROM
        swp_incident_impact
    WHERE
        isHighPotential = true
    UNION
    SELECT
        'PPS-T3' as category,
        count(*) as total
    FROM
        swp_incident_impact
    WHERE
        category.externalId = 'ITL-PPS-T3'
)
SELECT
    raw.category,
    raw.total as rawCount,
    dm.total as dmCount
FROM
    categoryCountRaw raw
    JOIN categoryCountDM dm ON (dm.category = raw.category)
WHERE
    dm.total <> raw.total