import asyncio
import traceback
from typing import Any

from cognite.client import CogniteClient
from loguru import logger

from entities.handler_input import HandlerInput
from entities.processor_result import ProcessorResult
from entities.teams_notification_input import TeamsNotificationInput
from infra.cognite_client_factory import create_cognite_client
from infra.date_utils import dt_format
from infra.env_variables import get_env_variables
from infra.logger import config_logger, get_log_messages
from infra.ms_teams_client import MsTeamsClient, create_ms_teams_client
from services.kpi_parameter_processor_service import KpiParameterProcessorService
from services.workflow_status_service import WorkflowStatusService


def handle(
    client: CogniteClient,
    data: dict[str, Any] | None = None,
) -> dict[str, Any] | None:
    """
    Handler function for the KPI Parameter Processor service.

    This function may call multiple kpi-parameters transformations and the kpi-calculator function

    Parameters:
        client: CogniteClient: Cognite client to use
        data: dict: Data to use for the execution. If not provided, default values will be used.

    Returns:
        `None` if data.is_workflow_task is False
        `dict[str, Any]` if data.is_workflow_task is True
    """

    handler_input = HandlerInput.parse(data)
    config_logger("DEBUG" if handler_input.debug else "INFO")

    logger.info(
        f"Starting execution of handler at {dt_format(handler_input.execution_time, True)}"
    )

    ms_teams_client = create_ms_teams_client()
    asyncio.run(
        WorkflowStatusService(
            client, ms_teams_client
        ).notify_previous_failed_execution_async(handler_input.interval_in_hours)
    )

    service = KpiParameterProcessorService(
        client=client,
        env_vars=get_env_variables(),
        handler_input=handler_input,
    )

    result: ProcessorResult | None = None
    try:
        result = asyncio.run(service.execute_async(handler_input.execution_time))
    except Exception as e:
        logger.error("An unexpected error has occurred")
        send_error_message(handler_input, result, ms_teams_client)
        raise e

    logger.success("Done!")
    return result.model_dump() if result else None


def send_error_message(
    input: HandlerInput,
    result: ProcessorResult | None,
    ms_teams_client: MsTeamsClient,
):
    output_msgs = "<br>".join(get_log_messages())
    exc_msg = traceback.format_exc().replace("\n", "<br>")
    msg = f"""
<b>Input:</b><br>
{input.model_dump()}<br>
<br>
<b>Output:</b><br>
{result.model_dump() if result else 'None'}<br>
<br>
<b>Logs:</b><br>
{output_msgs}<br>
<br>
<b>Stacktrace:</b><br>
{exc_msg}"""
    asyncio.run(
        ms_teams_client.send_message_async(
            TeamsNotificationInput(
                title="KPI Parameter Processor Error",
                text=msg,
                activity_messages=[],
            )
        )
    )


if __name__ == "__main__":
    client = create_cognite_client()
    handle(
        client,
        data={
            "dry_run": True,
            "process_all": False,
            "debug": True,
            "is_workflow_task": True,
        },
    )
