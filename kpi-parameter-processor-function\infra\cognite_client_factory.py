from functools import lru_cache

from cognite.client import Client<PERSON>onfig, CogniteClient
from cognite.client.credentials import Credential<PERSON>rovider, OAuthClientCredentials, Token

from .env_variables import EnvVariables, get_env_variables


class CogniteClientFactory:
    @staticmethod
    def _create_credentials(env_variables: EnvVariables) -> CredentialProvider:
        if env_variables.auth.token_override:
            return Token(env_variables.auth.token_override)

        return OAuthClientCredentials(
            token_url=env_variables.auth.token_uri,
            client_id=env_variables.auth.client_id,
            client_secret=env_variables.auth.secret,
            scopes=env_variables.auth.scopes,
        )

    @staticmethod
    def _create_client_config(env_variables: EnvVariables) -> ClientConfig:
        return ClientConfig(
            client_name=env_variables.cognite.client_name,
            project=env_variables.cognite.project,
            credentials=CogniteClientFactory._create_credentials(env_variables),
            base_url=env_variables.cognite.base_uri,
        )

    @staticmethod
    def create(
        env_variables: EnvVariables,
    ) -> CogniteClient:
        return CogniteClient(
            config=CogniteClientFactory._create_client_config(env_variables)
        )


@lru_cache
def create_cognite_client():
    """
    Create and cache an instance of the `CogniteClient` already authenticated
    """
    return CogniteClientFactory.create(get_env_variables())
