from functools import lru_cache

from cognite.client import Client<PERSON>onfig, CogniteClient
from cognite.client.credentials import Cred<PERSON><PERSON>rov<PERSON>, OAuthClientCredentials, Token

from .env_variables import EnvVariables, get_env_variables


class CogniteClientFactory:
    @staticmethod
    def _create_credentials(env_variables: EnvVariables) -> CredentialProvider:
        auth_variables = env_variables.auth
        if auth_variables.token_override:
            return Token(auth_variables.token_override)

        return OAuthClientCredentials(
            token_url=auth_variables.token_uri,
            client_id=auth_variables.client_id,
            client_secret=auth_variables.secret,
            scopes=auth_variables.scopes,
        )

    @staticmethod
    def _create_client_config(env_variables: EnvVariables) -> ClientConfig:
        cognite_variables = env_variables.cognite
        return ClientConfig(
            client_name=cognite_variables.client_name,
            project=env_variables.cognite.project,
            credentials=CogniteClientFactory._create_credentials(env_variables),
            base_url=cognite_variables.base_uri,
        )

    @staticmethod
    def create(
        env_variables: EnvVariables,
    ) -> CogniteClient:
        return CogniteClient(
            config=CogniteClientFactory._create_client_config(env_variables)
        )


class HashableCogniteClient:
    def __init__(self, client: CogniteClient) -> None:
        self.client = client

    def __eq__(self, value: object, /) -> bool:
        if not isinstance(value, HashableCogniteClient):
            return False
        return hash(value) == hash(self)

    def __hash__(self) -> int:
        return hash(self.client.config.credentials.authorization_header)


@lru_cache
def create_cognite_client():
    return CogniteClientFactory.create(get_env_variables())
