import atexit
import os

from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import Token
from msal import PublicClientApplication, SerializableTokenCache

CLIENT_NAME = "celanese-development-leocardo"
BASE_URL = "https://az-eastus-1.cognitedata.com"


def create_client(
    project: str, client_name: str = CLIENT_NAME, base_url: str = BASE_URL
):
    creds = _get_credentials()
    credentials = Token(creds["access_token"])
    config = ClientConfig(
        client_name=client_name,
        project=project,
        base_url=base_url,
        credentials=credentials,
    )
    return CogniteClient(config=config)


def _get_credentials():
    authority = "https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37"
    client_id = "1fbf5ca4-0dda-4fc0-bf1e-3648abf580ec"
    scopes = [
        "https://az-eastus-1.cognitedata.com/.default",
    ]
    port = "53000"
    app = PublicClientApplication(
        client_id=client_id, authority=authority, token_cache=_create_cache()
    )
    accounts = app.get_accounts()
    if accounts:
        return app.acquire_token_silent(scopes, account=accounts[0])

    return app.acquire_token_interactive(
        scopes=scopes,
        port=port,
    )


def _create_cache():
    cache_file_name = "cache.bin"
    cache = SerializableTokenCache()
    if os.path.exists(cache_file_name):
        cache.deserialize(open(cache_file_name).read())
    atexit.register(
        lambda: open(cache_file_name, "w").write(cache.serialize())
        if cache.has_state_changed
        else None
    )
    return cache
