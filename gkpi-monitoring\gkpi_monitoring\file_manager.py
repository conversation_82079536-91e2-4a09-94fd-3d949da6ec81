import io
import uuid
from datetime import datetime
from typing import Any, Literal, Union

import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import NodeId
from cognite.client.data_classes.data_modeling.cdm.v1 import (
    CogniteFile,
    CogniteFileApply,
)

from gkpi_monitoring.models import DownloadFileResponse, UploadFileResponse

EXTENSIONS = Literal["csv", "parquet"]
VALID_DATA = Union[pd.DataFrame, list[dict[str, Any]], bytes, bytearray, io.BytesIO]


class FileManager:
    def __init__(self, cognite_client: CogniteClient, default_space: str):
        self._cognite_client = cognite_client
        self._default_space = default_space

    def upload_file(
        self,
        file_name: str,
        data: VALID_DATA,
        extension: EXTENSIONS,
    ) -> UploadFileResponse:
        output = self._generate_bytes(data, extension)

        node_id = self._generate_instance_id(file_name, extension)
        self._cognite_client.data_modeling.instances.apply(
            CogniteFileApply(
                external_id=node_id.external_id,
                space=node_id.space,
                name=node_id.external_id,
                description=str(uuid.uuid4()),
            )
        )

        _ = self._cognite_client.files.upload_content_bytes(
            instance_id=node_id,
            content=output.getvalue(),
        )

        response = self._cognite_client.files.retrieve_download_urls(
            instance_id=node_id,
            extended_expiration=True,
        )

        return UploadFileResponse(
            external_id=node_id.external_id,
            space=node_id.space,
            download_url=response[node_id],
        )

    def download_file(
        self, file_name: str, extension: EXTENSIONS
    ) -> DownloadFileResponse | None:
        instance_id = self._generate_instance_id(file_name, extension)

        metadata = self._cognite_client.data_modeling.instances.retrieve_nodes(
            nodes=instance_id, node_cls=CogniteFile
        )
        if not metadata:
            return None

        bytes_data = self._cognite_client.files.download_bytes(instance_id=instance_id)
        return DownloadFileResponse(
            data=bytes_data,
            last_updated_time=datetime.fromtimestamp(metadata.last_updated_time / 1000),
        )

    def _generate_instance_id(self, file_name: str, extension: EXTENSIONS) -> NodeId:
        return NodeId(
            external_id="GKPI-LOG-" + file_name + "." + extension,
            space=self._default_space,
        )

    def _generate_bytes(self, data: VALID_DATA, extension: EXTENSIONS) -> io.BytesIO:
        if isinstance(data, io.BytesIO):
            data.seek(0)
            return data

        if isinstance(data, (bytes, bytearray)):
            bio = io.BytesIO(data)
            bio.seek(0)
            return bio

        df = data if isinstance(data, pd.DataFrame) else pd.DataFrame(data)

        output = io.BytesIO()

        if extension == "csv":
            df.to_csv(output, index=False)
        elif extension == "parquet":
            df.to_parquet(output, index=False)
        else:
            raise ValueError(f"Unsupported file extension: {extension}")

        output.seek(0)
        return output
