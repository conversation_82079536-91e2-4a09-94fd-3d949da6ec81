import os
from pathlib import Path
from typing import Any, Literal, Self

import toml
from dotenv import load_dotenv
from pydantic import BaseModel, FilePath, HttpUrl, model_validator


def _inject_env_variables(config: dict[str, Any]) -> dict[str, Any]:
    prefix = "${var."
    sufix = "}"
    for section, values in config.items():
        if isinstance(values, dict):
            config[section] = _inject_env_variables(values)
        elif (
            isinstance(values, str)
            and values.startswith(prefix)
            and values.endswith(sufix)
        ):
            env_var = values.removeprefix(prefix).removesuffix(sufix)
            entry = os.getenv(env_var)
            if not entry:
                print(f"Env Variable {env_var} not set")
            config[section] = entry or values
    return config


class ClientCredentials(BaseModel):
    client_id: str
    client_secret: str
    token_url: HttpUrl
    scopes: list[HttpUrl]


class Credentials(BaseModel):
    client_credentials: ClientCredentials


class CogniteConfig(BaseModel):
    client_name: str
    project: str
    base_url: HttpUrl
    credentials: Credentials
    default_space: str


class ResourcesConfig(BaseModel):
    workflows: list[str]
    functions: list[str]
    transformations: list[str]


class NotificationConfig(BaseModel):
    teams_url: str
    teams_notification_infra_template: FilePath
    teams_notification_data_template: FilePath


class DataSourceConfig(BaseModel):
    space: str
    table: str
    origin: Literal["fdm", "raw"]
    alias: str


class DataValidationConfig(BaseModel):
    description: str
    sql_file: FilePath


class DataTestConfig(BaseModel):
    output_path: Path
    force_update_cache: bool = False
    data_sources: list[DataSourceConfig]
    validations: list[DataValidationConfig]

    @model_validator(mode="after")
    def check_data_sources(self) -> Self:
        if not self.data_sources:
            raise ValueError("No data sources provided")

        visited = set[str]()
        for source in self.data_sources:
            if source.alias in visited:
                raise ValueError(f"Duplicated alias {source.alias}")
            visited.add(source.alias)

        return self


class Config(BaseModel):
    resources: ResourcesConfig
    cognite: CogniteConfig
    notification: NotificationConfig
    data_test: DataTestConfig

    @classmethod
    def from_toml(cls, toml_path: Path) -> "Config":
        load_dotenv()
        entry = toml.load(toml_path)
        entry = _inject_env_variables(entry)
        return cls.model_validate(entry)
