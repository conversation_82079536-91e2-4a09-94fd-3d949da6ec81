from entities.raw_kpi_parameter import RawKpiParameter
from infra.async_utils import run_sync
from repositories.repository_base import RepositoryBase


class KpiParameterRepository(RepositoryBase):
    async def get_all_raw_async(self) -> list[RawKpiParameter]:
        """
        Get all kpi parameters from the staging area table
        """
        return await run_sync(self._get_all_raw)

    def _get_all_raw(self) -> list[RawKpiParameter]:
        raw_result = self._cognite_client.raw.rows.list(
            db_name=self._env_vars.cognite.raw_ref_data_db,
            table_name=self._env_vars.cognite.raw_ref_kpi_param_table,
            limit=None,
        )
        return [RawKpiParameter(**r.columns) for r in raw_result if r.columns]
