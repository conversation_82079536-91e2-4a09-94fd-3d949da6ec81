from jinja2 import Template

from gkpi_monitoring.config import Config
from gkpi_monitoring.models import DataError, UploadFileResponse


class TemplateService:
    def __init__(self, config: Config):
        infra_content = (
            config.notification.teams_notification_infra_template.read_text()
        )
        data_content = config.notification.teams_notification_data_template.read_text()
        self._teams_notification_infra_template: Template = Template(
            source=infra_content
        )
        self._teams_notification_data_template: Template = Template(source=data_content)

    def render_teams_notification_infra(
        self,
        total_issues: int,
        file: UploadFileResponse,
    ) -> str:
        return self._teams_notification_infra_template.render(
            {"total_issues": total_issues, "file": file}
        )

    def render_teams_notification_data(self, errors: list[DataError]) -> str:
        return self._teams_notification_data_template.render({"errors": errors})
