trigger:
  branches:
    include:
      - dev
      - qa
      - prod
  paths:
    include:
      - workflows/*

variables:
  - group: kpi-dom
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(PROD_SECRET)
    ${{ else }}:
      value: $(DSD_SECRET)
  - name: pythonEnv
    ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: qa
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: prod
    ${{ else }}:
      value: dev

stages:
  - stage: PublishCogniteDataWorkflow
    displayName: "Publish Workflow to Cognite for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: PublishWorkflows
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.11"
            displayName: "Use python 3.11"

          - script: |
              cd $(System.DefaultWorkingDirectory)/cognite-resource-deployment
              python -m pip install --upgrade pip
              pip install -r requirements.txt
            displayName: 'Install dependencies'

          - script: |
              cd $(System.DefaultWorkingDirectory)/cognite-resource-deployment
              IFS=',' read -r -a array <<< "$FORCE_DEPLOY"
              python -m deploy_cognite_workflow --force ${array[@]}
            displayName: 'Deploy Workflows'
            env:
              FORCE_DEPLOY: $(forceDeploy)
              AUTH_TOKEN_OVERRIDE: $(tokenOverride)
              PYTHON_ENV: ${{ variables.pythonEnv }}
              AUTH_SECRET: ${{ variables.authSecret }}
