from cognite.client import CogniteClient
from industrial_model import DataModelId, Engine, col, select

from models.kpi_entitie import CoreImpactCategory, Kpi, KpiResult


class IndustrialModelRepository:
    def __init__(self, cognite_client: CogniteClient) -> None:
        self.engine = Engine(
            cognite_client=cognite_client,
            data_model_id=DataModelId(
                external_id="KPIDOM", space="KPI-COR-ALL-DMD", version="1_1_1"
            ),
        )

    def get_results_by_kpi_codes(self, kpi_codes: list[str]) -> list[KpiResult]:
        statement = select(KpiResult).where(
            col(KpiResult.kpi).nested_(col(Kpi.code).in_(kpi_codes))
        )
        return self.engine.query_all_pages(statement)

    def get_results_by_kpi_group(self, kpi_group: str) -> list[KpiResult]:
        statement = select(KpiResult).where(
            col(KpiResult.kpi).nested_(
                col(Kpi.category).nested_(
                    col(CoreImpactCategory.name).equals_(kpi_group)
                )
            )
        )
        return self.engine.query_all_pages(statement)
