from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import Credential<PERSON>rovider, OAuthClientCredentials, Token

COGNITE_CLIENT_NAME = "celanese-development-leocardo"
TOKEN_URL = "https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37/oauth2/v2.0/token"
COGNITE_BASE_URL = "https://az-eastus-1.cognitedata.com"
scopes = [
    "https://az-eastus-1.cognitedata.com/.default",
]


def _create_credentials(
    cognite_client_id: str, cognite_client_secret: str
) -> CredentialProvider:
    return OAuthClientCredentials(
        token_url=TOKEN_URL,
        client_id=cognite_client_id,
        client_secret=cognite_client_secret,
        scopes=scopes,
    )


def _create_client_config(
    cognite_project: str, cognite_client_id: str, cognite_client_secret: str
) -> ClientConfig:
    return ClientConfig(
        client_name=COGNITE_CLIENT_NAME,
        project=cognite_project,
        credentials=_create_credentials(cognite_client_id, cognite_client_secret),
        base_url=COGNITE_BASE_URL,
        timeout=600,
    )


def create_client(
    cognite_project: str, cognite_client_id: str, cognite_client_secret: str
) -> CogniteClient:
    return CogniteClient(
        config=_create_client_config(
            cognite_project, cognite_client_id, cognite_client_secret
        )
    )
