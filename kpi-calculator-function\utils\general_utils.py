def generate_query(list_name: str, selected_items: str) -> str:
    return f"""
        query Query($first: Int, $after: String, $filter: _{list_name[0].upper() + list_name[1:]}Filter) {{
            {list_name}(first: $first, after: $after, filter: $filter) {{
                items {{
                    {selected_items}
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
        """
