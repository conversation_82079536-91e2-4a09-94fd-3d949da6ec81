from datetime import UTC, datetime

from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
)

from models.kpi_entitie import KpiResult
from repositories.industrial_model_repository import IndustrialModelRepository
from services.graphql_service import GraphqlService
from utils.general_utils import generate_query


class KPIDOMService:
    def __init__(
        self,
        graphql: GraphqlService,
        industrial_model_repository: IndustrialModelRepository,
    ):
        self.graphql = graphql
        self.industrial_model_repository = industrial_model_repository

    def get_kpis_by_group(self, kpi_group: str) -> list[dict]:
        return self.industrial_model_repository.get_results_by_kpi_group(kpi_group)

    def get_kpis_by_codes(self, kpi_codes: list[str]) -> list[KpiResult]:
        return self.industrial_model_repository.get_results_by_kpi_codes(kpi_codes)

    async def get_kpis(self, kpi_filter) -> list[dict]:
        kpi_result_selection = """
            externalId
            kpi{
                code
                name
                formula
                parameters (first: 1000) {
                    items{
                        externalId
                        code
                        name
                    }
                }
            }
            parameterValues (first: 1000) {
                items{
                    externalId
                    parameter {
                        externalId
                        code
                        name
                    }
                    values{
                        externalId
                    }
                }
            }
            timeGranularity{
                code
                name
            }
            value{
                externalId
            }
            """

        return await self.graphql.get_all_results_list(
            generate_query("listKpiResult", kpi_result_selection),
            "listKpiResult",
            kpi_filter,
            first=1000,
        )

    async def get_entries_updated_kpis_(self, kpi_codes: list[str], version):
        kpi_selection = """
            externalId
            code
            lastExecutionDateTime
            """
        kpi_filter = {"code": {"in": kpi_codes}}
        kpis = await self.graphql.get_all_results_list(
            generate_query("listKpi", kpi_selection),
            "listKpi",
            kpi_filter,
            first=100,
        )

        for kpi in kpis:
            kpi["lastExecutionDateTime"] = (
                datetime.now(UTC).replace(microsecond=0).isoformat()
            )

        entry_kpis = [
            NodeApply(
                space="KPI-COR-ALL-DAT",
                external_id=kpi["externalId"],
                sources=[
                    NodeOrEdgeData(
                        source=ViewId(
                            space="KPI-COR-ALL-DMD",
                            external_id="Kpi",
                            version=version,
                        ),
                        properties={k: v for k, v in kpi.items() if k != "externalId"},
                    )
                ],
            )
            for kpi in kpis
        ]
        return entry_kpis
