schedules:
  - cron: "0 */6 * * *"
    displayName: "Run tests every 6 hours"
    branches:
      include:
      - dev
    always: true

variables:
  - group: 'gkpi-monitoring'

stages:
  - stage: Infra_Monitoring
    jobs:
      - job: Validate
        pool:
          vmImage: "ubuntu-latest"
        displayName: Infra Monitoring - GKPI
        steps:
          - checkout: self
            fetchTags: true
            clean: true
            persistCredentials: true

          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.11"
              addToPath: true

          - script: |
              cd $(System.DefaultWorkingDirectory)/gkpi-monitoring
              python -m pip install --upgrade pip
              pip install uv
            displayName: "Install dependencies"

          - script: |
              cd $(System.DefaultWorkingDirectory)/gkpi-monitoring
              python -m uv run main.py monitor-infra
            displayName: "Monitoring Infra"
            env:
              COGNITE_CLIENT_ID: $(COGNITE_CLIENT_ID) 
              COGNITE_CLIENT_SECRET: $(COGNITE_CLIENT_SECRET) 
              COGNITE_PROJECT: $(COGNITE_PROJECT)  

          - script: |
              cd $(System.DefaultWorkingDirectory)/gkpi-monitoring
              python -m uv run main.py monitor-data
            displayName: "Monitoring Data"
            env:
              COGNITE_CLIENT_ID: $(COGNITE_CLIENT_ID) 
              COGNITE_CLIENT_SECRET: $(COGNITE_CLIENT_SECRET) 
              COGNITE_PROJECT: $(COGNITE_PROJECT)  