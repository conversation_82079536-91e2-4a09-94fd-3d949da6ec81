from typing import Any, Literal

from cognite.client.data_classes import (
    DynamicTaskParameters,
    FunctionTaskParameters,
    TransformationTaskParameters,
    WorkflowDefinitionUpsert,
    WorkflowTask,
    WorkflowTriggerUpsert,
    WorkflowVersionId,
    WorkflowVersionList,
    WorkflowVersionUpsert,
)
from cognite.client.data_classes.workflows import (
    WorkflowScheduledTriggerRule,
)
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel

from ..utils import has_duplicates, is_valid_cron

DEFAULT_MODEL_CONFIG = ConfigDict(
    alias_generator=to_camel,
    populate_by_name=True,
    from_attributes=True,
    extra="ignore",
)


class WorkflowTaskParameterConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str = ""
    concurrency_policy: (
        Literal["fail", "restartAfterCurrent", "waitForCurrent"] | None
    ) = None
    data: dict[str, Any] | str | None = None
    tasks: str = ""


class WorkflowTaskConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str
    name: str | None = None
    description: str | None = None
    type: Literal["function", "transformation", "dynamic"]
    on_failure: Literal["abortWorkflow", "skipTask"] = "abortWorkflow"
    retries: int = 3
    timeout: int = 3600
    depends_on: list[str] | None = None
    parameters: WorkflowTaskParameterConfig

    def to_task(self) -> WorkflowTask:
        return WorkflowTask(
            external_id=self.external_id,
            name=self.name,
            description=self.description,
            retries=self.retries,
            timeout=self.timeout,
            on_failure=self.on_failure,
            depends_on=self.depends_on,
            parameters=self.get_parameters(),
        )

    def get_parameters(
        self,
    ) -> FunctionTaskParameters | TransformationTaskParameters | DynamicTaskParameters:
        match self.type:
            case "function":
                return FunctionTaskParameters(
                    external_id=self.parameters.external_id,
                    data=self.parameters.data,
                )
            case "transformation":
                return TransformationTaskParameters(
                    external_id=self.parameters.external_id,
                    concurrency_policy=self.parameters.concurrency_policy,  # type: ignore
                    use_transformation_credentials=True,
                )
            case "dynamic":
                return DynamicTaskParameters(tasks=self.parameters.tasks)


class WorkflowScheduleConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str
    cron: str
    data: dict[str, Any] | None = None

    def is_valid(self):
        return is_valid_cron(self.cron)


class WorkflowConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str
    version: str = "1_0_0"
    schedules: list[WorkflowScheduleConfig] | None = None
    description: str | None = None
    tasks: list[WorkflowTaskConfig]

    def validate_workflow(
        self, existing_versions: WorkflowVersionList
    ) -> tuple[bool, list[str]]:
        errors = []
        if not validate_workflow_external_id(self.external_id):
            errors.append(f"Workflow {self.external_id} has invalid external_id")

        if not self.tasks:
            errors.append(f"Workflow {self.external_id} must have a tasks")

        if not has_valid_version(self.version):
            errors.append(f"Workflow {self.external_id} has invalid version")

        all_tasks_ids = {t.external_id for t in self.tasks}
        is_depends_on_invalid = any(
            [
                t
                for t in self.tasks
                if not set(t.depends_on or []).issubset(all_tasks_ids)
            ]
        )
        if is_depends_on_invalid:
            errors.append(
                f"Workflow {self.external_id} has tasks which depends on missing tasks"
            )
        existing_version_ids = [
            int(e.version)
            for e in existing_versions
            if e.workflow_external_id == self.external_id
        ]
        if existing_version_ids and max(existing_version_ids) > int(self.version):
            errors.append(
                f"Workflow {self.external_id} already have a newer version deployed"
            )

        tasks_ids = [t.external_id for t in self.tasks]
        if has_duplicates(tasks_ids):
            errors.append(
                f"Duplicated task external ids found in workflow {self.external_id}"
            )

        if self.schedules:
            if any(not s.is_valid() for s in self.schedules):
                errors.append(
                    f"Invalid cron expression for workflow {self.external_id}"
                )
            if has_duplicates([s.external_id for s in self.schedules]):
                errors.append(f"Duplicated schedule id for workflow {self.external_id}")

        return len(errors) == 0, errors

    @property
    def version_id(self):
        return WorkflowVersionId(self.external_id, self.version)

    def to_version_upsert(self) -> WorkflowVersionUpsert:
        return WorkflowVersionUpsert(
            workflow_external_id=self.external_id,
            version=self.version,
            workflow_definition=WorkflowDefinitionUpsert(
                tasks=[t.to_task() for t in self.tasks],
                description=self.description,
            ),
        )

    def to_triggers_upsert(self) -> list[WorkflowTriggerUpsert]:
        assert self.schedules
        return [
            WorkflowTriggerUpsert(
                external_id=s.external_id,
                workflow_external_id=self.external_id,
                workflow_version=self.version,
                trigger_rule=WorkflowScheduledTriggerRule(cron_expression=s.cron),
            )
            for s in self.schedules
        ]

    def get_all_transformations_ids(self):
        return [
            t.parameters.external_id for t in self.tasks if t.type == "transformation"
        ]

    def get_all_function_ids(self):
        return [t.parameters.external_id for t in self.tasks if t.type == "function"]


def has_valid_version(version: str):
    version_arr = version.split("_")
    return (
        version_arr
        or len(version_arr) != 3
        or any(not v.isdecimal() for v in version_arr)
    )


def validate_workflow_external_id(
    file_name: str, valid_prefixes: list[str] | None = None
) -> bool:
    if not file_name.startswith("wf-"):
        return False
    if not valid_prefixes:
        return True
    file_name_without_wf = file_name.replace("wf-", "")
    return any(
        True
        for prefix in valid_prefixes
        if file_name_without_wf.startswith(prefix + "-")
    )
