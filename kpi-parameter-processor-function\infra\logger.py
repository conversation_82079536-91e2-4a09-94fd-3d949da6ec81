import os
import sys
from functools import lru_cache

from loguru import logger

log_messages: list[str] = []


@lru_cache
def supports_color():
    supported_platform = sys.platform != "win32" or "ANSICON" in os.environ
    is_a_tty = hasattr(sys.stdout, "isatty") and sys.stdout.isatty()
    return supported_platform and is_a_tty


@lru_cache
def config_logger(level: str = "info"):
    logger.remove()
    logger.add(sys.stdout, colorize=supports_color(), level=level)
    logger.add(lambda msg: log_messages.append(msg), level=level)


def get_log_messages():
    return log_messages
