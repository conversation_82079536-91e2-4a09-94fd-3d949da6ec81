import os
from functools import lru_cache

import pymsteams

from entities.teams_notification_input import TeamsNotificationInput
from infra.env_variables import get_env_variables


class MsTeamsClient:
    def __init__(self, uri: str | None, prefix: str = "[Local]"):
        self._uri = uri
        self._prefix = prefix

    async def send_message_async(self, message: str | TeamsNotificationInput) -> bool:
        if not self._uri:
            return False
        try:
            connector = pymsteams.async_connectorcard(self._uri)

            if isinstance(message, str):
                connector.text(f"[{self._prefix}] {message}")
            else:
                for section in message.to_card_sections():
                    connector.addSection(section)
                connector.text(message.text)
                connector.title(f"[{self._prefix}] {message.title}")
                if message.button_text and message.button_action_link:
                    connector.addLinkButton(
                        buttontext=message.button_text,
                        buttonurl=message.button_action_link,
                    )
            await connector.send()
            return True
        except Exception:
            return False


@lru_cache
def create_ms_teams_client():
    env = get_env_variables()
    python_env = os.getenv("PYTHON_ENV", "local").lower()
    return MsTeamsClient(env.auth.teams_notification_webhook_url, python_env)
