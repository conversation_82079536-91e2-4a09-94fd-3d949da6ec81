from typing import Any

from cognite.client.data_classes.functions import RunTime
from pydantic import BaseModel, Field


class ScheduleParameters(BaseModel):
    schedule_name: str = Field(alias="scheduleName")
    cron: str
    data: dict[str, Any] = {}


class FunctionConfiguration(BaseModel):
    external_id: str = Field(alias="externalId")
    runtime: RunTime
    schedules: list[ScheduleParameters]
    pipeline_variables: list[str] = Field(
        alias="pipelineVariables", default_factory=list
    )
