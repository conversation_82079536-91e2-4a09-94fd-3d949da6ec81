from typing import Any

from cognite.client import CogniteClient

from infra.async_utils import run_sync
from infra.env_variables import EnvVariables


class RepositoryBase:
    def __init__(self, cognite_client: CogniteClient, env_vars: EnvVariables) -> None:
        self._cognite_client = cognite_client
        self._env_vars = env_vars

    async def _gql_query_async(
        self,
        query: str,
        variables: dict[str, Any] | None = None,
    ):
        return await run_sync(self._gql_query, query, variables)

    def _gql_query(
        self,
        query: str,
        variables: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        return self._cognite_client.data_modeling.graphql.query(
            id=self._env_vars.cognite.kpi_data_model_id,
            query=query,
            variables=variables,
        )
