import datetime
from typing import Literal, cast, Optional

from cognite.client.data_classes import (
    FunctionCall,
    TransformationJob,
    WorkflowExecution,
)
from pydantic import BaseModel, computed_field

ResourceType = Literal["transformation", "function", "workflow"]
ResourceStatus = Literal["completed", "failed", "terminated"]
HealthyStatus = Literal["UNHEALTHY", "HEALTHY", "ATTENTION"]


class RawStatement(BaseModel):
    table: str
    database: str
    column: str

    @classmethod
    def from_statement(cls, query: str) -> "RawStatement | None":
        if not query.startswith("RAW:"):
            return None
        items = query.split(":")
        if len(items) != 4:
            raise ValueError(f"Raw value {query} is incorrect")

        return RawStatement(database=items[1], table=items[2], column=items[3])


class ResourceExecution(BaseModel):
    resource_type: ResourceType
    status: ResourceStatus
    start_time: datetime.datetime
    end_time: datetime.datetime

    @classmethod
    def from_cognite_resource(
        cls, item: TransformationJob | FunctionCall | WorkflowExecution
    ) -> "ResourceExecution | None":
        resource_type_map: dict[str, ResourceType] = {
            TransformationJob.__name__: "transformation",
            FunctionCall.__name__: "function",
            WorkflowExecution.__name__: "workflow",
        }

        resource_type = resource_type_map.get(item.__class__.__name__)
        if not resource_type:
            raise ValueError(
                f"Unsupported resource type: {item.__class__.__name__} | {list(resource_type_map.keys())}"
            )

        start_time = getattr(item, "started_time", None) or getattr(
            item, "start_time", None
        )
        end_time = getattr(item, "finished_time", None) or getattr(
            item, "end_time", None
        )

        if not start_time or not end_time or not item.status:
            return None

        return cls(
            resource_type=resource_type,
            start_time=datetime.datetime.fromtimestamp(start_time / 1000),
            end_time=datetime.datetime.fromtimestamp(end_time / 1000),
            status=cast(ResourceStatus, item.status.lower()),
        )


class ResourceMetrics(BaseModel):
    external_id: str
    resource_type: ResourceType

    last_status: ResourceStatus | None = None
    last_execution: datetime.datetime | None = None

    completed_count: int = 0
    failed_count: int = 0
    terminated_count: int = 0

    @computed_field  # type: ignore
    @property
    def healthy_status(self) -> HealthyStatus:
        if (
            self.failed_count > 0 and self.last_status == "completed"
        ) or self.last_status is None:
            return "ATTENTION"
        if self.failed_count == 0 and self.terminated_count == 0:
            return "HEALTHY"
        return "UNHEALTHY"

    @classmethod
    def from_resource_executions(
        cls,
        external_id: str,
        resource_type: ResourceType,
        resource_executions: list[ResourceExecution],
    ) -> "ResourceMetrics":
        entry = cls(
            external_id=external_id,
            resource_type=resource_type,
        )

        for result_item in resource_executions:
            if result_item.status == "completed":
                entry.completed_count += 1
            elif result_item.status == "failed":
                entry.failed_count += 1
            elif result_item.status == "terminated":
                entry.terminated_count += 1

            if (
                entry.last_execution is None
                or entry.last_execution < result_item.start_time
            ):
                entry.last_status = result_item.status
                entry.last_execution = result_item.start_time
        return entry


class UploadFileResponse(BaseModel):
    external_id: str
    space: str
    download_url: str


class DownloadFileResponse(BaseModel):
    data: bytes
    last_updated_time: datetime.datetime


class DataError(BaseModel):
    file: UploadFileResponse
    description: str
    id: Optional[int] = None
