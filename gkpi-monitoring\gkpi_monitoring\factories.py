from cognite.client import CogniteClient

from gkpi_monitoring.authentication import create_client
from gkpi_monitoring.config import Config
from gkpi_monitoring.data_monitor_executor import DataMonitorExecutor
from gkpi_monitoring.file_manager import FileManager
from gkpi_monitoring.infra_monitor_executor import InfraMonitorExecutor
from gkpi_monitoring.notification import NotificationService
from gkpi_monitoring.template import TemplateService


def _create_cognite_client(config: Config) -> CogniteClient:
    return CogniteClient.load(config.cognite.model_dump(mode="json"))


def _create_template_service(config: Config) -> TemplateService:
    return TemplateService(config)


def _create_file_manager(cognite_client: CogniteClient, config: Config) -> FileManager:
    return FileManager(cognite_client, config.cognite.default_space)


def _create_notification_service(config: Config) -> NotificationService:
    return NotificationService(config.notification)


def create_infra_monitor_executor(config: Config) -> InfraMonitorExecutor:
    cognite_client = _create_cognite_client(config)
    file_manager = _create_file_manager(cognite_client, config)
    notification_service = _create_notification_service(config)
    template_service = _create_template_service(config)

    return InfraMonitorExecutor(
        cognite_client, file_manager, notification_service, template_service
    )


def create_data_monitor_executor(
    config: Config, debug: bool = False
) -> DataMonitorExecutor:
    if debug:
        cognite_client = create_client("celanese-dev")
    else:
        cognite_client = _create_cognite_client(config)

    file_manager = _create_file_manager(cognite_client, config)
    notification_service = _create_notification_service(config)
    template_service = _create_template_service(config)

    return DataMonitorExecutor(
        cognite_client,
        config.data_test,
        file_manager,
        notification_service,
        template_service,
    )
