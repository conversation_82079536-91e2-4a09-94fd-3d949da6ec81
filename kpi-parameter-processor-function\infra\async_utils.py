from collections.abc import Callable
from typing import TypeVar

from anyio import to_thread

T_Retval = TypeVar("T_Retval")


async def run_sync(
    func: Callable[..., T_Retval],
    *args: object | None,
    abandon_on_cancel: bool = False,
):
    """
    Calls a synchronous function in a worker thread making it asynchronous

    *Important Note*: It's meant to be used in IO bound tasks, not CPU tasks.

    Parameters:
        func: A callable function
        args: The positional arguments of the `func`
        abandon_on_cancel: `True` to abandon the thread (leaving it to run unchecked)
            if the host task is cancelled, `False` to ignore cancellations in the host
            task until the operation has completed in the worker thread (blocking)
    """
    return await to_thread.run_sync(func, *args, abandon_on_cancel=abandon_on_cancel)
