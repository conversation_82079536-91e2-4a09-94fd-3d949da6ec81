from cognite.client.data_classes import TransformationTaskParameters
from cognite.client.data_classes.workflows import WorkflowTask

from entities.raw_kpi_parameter import RawKpiParameter


class WorkflowTaskFactory:
    @staticmethod
    def from_parameters(parameters: list[RawKpiParameter]):
        unique_transformations = {p.ingestion_transformation for p in parameters}
        return [
            WorkflowTask(
                external_id=f"wft-dynamic-kpi-parameter-processor::{t}",
                parameters=TransformationTaskParameters(
                    external_id=t,
                    concurrency_policy="restartAfterCurrent",
                ),
            ).dump()
            for t in unique_transformations
        ]
