from datetime import datetime

import cognite.client.data_classes.filters as filters
from cognite.client import CogniteClient
from cognite.client.data_classes.time_series import TimeSeriesProperty

from infra.async_utils import run_sync


class TimeSeriesRepository:
    def __init__(self, client: CogniteClient) -> None:
        self._cognite_client = client

    async def get_by_parameter_code_async(self, parameter_codes: set[str]):
        """
        Return all time series filtering by using the parameter code as a prefix

        Parameters:
            parameter_codes: List of parameter codes to filter
        """
        return await run_sync(
            lambda: self._cognite_client.time_series.filter(
                filter=filters.Or(
                    *[
                        filters.Prefix(
                            TimeSeriesProperty.external_id, f"KPI-PARAM:{p}:"
                        )
                        for p in parameter_codes
                    ]
                ),
                limit=None,
            )
        )

    async def delete_data_points_async(
        self,
        time_series_ids: set[int],
        start: datetime,
        end: datetime,
    ) -> None:
        """
        An async wrapper for the `CogniteClient` delete_ranges method used to delete
        a range of datapoints from multiple time series.

        Parameters:
            time_series_ids: List of time series that will have their data points erased
            start: Inclusive start date of the range
            end: Exclusive end date of the range
        """
        return await run_sync(
            lambda: self._cognite_client.time_series.data.delete_ranges(
                [
                    {
                        "id": id,
                        "start": start,
                        "end": end,
                    }
                    for id in time_series_ids
                ]
            )
        )
