import json
import os
import sys
from datetime import date

import aiohttp

from models.location_tree import Location<PERSON>reeNode, LocationTreeReponse
from services.logging_service import LoggingService

from .env_variables import CogniteVariables

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class CorityClient:
    def __init__(
        self, logging_service: LoggingService, cority_env_variables: CogniteVariables
    ):
        self._log = logging_service
        self.cority_env_variables = cority_env_variables

    async def get_auth_token(self):
        try:
            async with aiohttp.ClientSession() as async_client_session:
                url = f"{self.cority_env_variables.base_uri}/token"
                request_header = {
                    "Authorization": f"Bearer {self.cority_env_variables.access_key}"
                }
                async with async_client_session.get(
                    url, headers=request_header
                ) as response:
                    response.raise_for_status()
                    response_data = await response.json()
                    self.headers = {
                        "Authorization": f"Bearer {response_data['AccessToken']}",
                        "Content-Type": "application/json",
                    }

        except aiohttp.ClientError as client_err:
            self._log.error(f"Error in Cority API request. Error: {str(client_err)}")

    async def get_location_tree_nodes(self, page_index: int = 1) -> LocationTreeReponse:
        try:
            async with aiohttp.ClientSession() as async_client_session:
                url = f"{self.cority_env_variables.base_uri}/treemaster?exactmatch=true&PageSize=200&PageIndex={page_index}"
                async with async_client_session.get(
                    url, headers=self.headers
                ) as response:
                    response.raise_for_status()
                    response_data = await response.json()

            return LocationTreeReponse(**response_data)

        except aiohttp.ClientError as client_err:
            self._log.error(f"Error in Cority API request. Error: {str(client_err)}")

    async def add_location_tree_node(
        self, code: str, description: str, parent_id: int
    ) -> LocationTreeNode:
        request_body = {
            "Code": code,
            "Description": description,
            "startDate": date.today().strftime("%m/%d/%Y"),
            "endDate": None,
            "inactive": False,
            "parent": {
                "id": parent_id,
            },
            "treeConnector": {},
        }
        request_body_json = json.dumps(request_body)

        try:
            async with aiohttp.ClientSession() as async_client_session:
                url = f"{self.cority_env_variables.base_uri}/treemaster"
                async with async_client_session.post(
                    url, data=request_body_json, headers=self.headers
                ) as response:
                    response.raise_for_status()
                    response_data = await response.json()

            return LocationTreeNode(**response_data["record"])

        except aiohttp.ClientError as client_err:
            self._log.error(f"Error in Cority API request. Error: {str(client_err)}")

    async def update_location_tree_node(
        self, inactive: bool, description: str, node_id: int
    ) -> bool:
        request_body = {"inactive": inactive, "description": description}
        request_body_json = json.dumps(request_body)

        try:
            async with aiohttp.ClientSession() as async_client_session:
                url = f"{self.cority_env_variables.base_uri}/treemaster/{node_id}"
                async with async_client_session.put(
                    url, data=request_body_json, headers=self.headers
                ) as response:
                    response.raise_for_status()
                    status_code = response.status

            return status_code == 200

        except aiohttp.ClientError as client_err:
            self._log.error(f"Error in Cority API request. Error: {str(client_err)}")

    async def activate_location_tree_node(
        self, node_id: int, description: str, parent_id: int
    ) -> LocationTreeNode:
        request_body = {
            "inactive": False,
            "description": description,
            "endDate": None,
            "parent": {"id": parent_id},
        }
        request_body_json = json.dumps(request_body)

        try:
            async with aiohttp.ClientSession() as async_client_session:
                url = f"{self.cority_env_variables.base_uri}/treemaster/{node_id}"
                async with async_client_session.put(
                    url, data=request_body_json, headers=self.headers
                ) as response:
                    response.raise_for_status()
                    response_data = await response.json()

            return LocationTreeNode(**response_data["record"])

        except aiohttp.ClientError as client_err:
            self._log.error(f"Error in Cority API request. Error: {str(client_err)}")
