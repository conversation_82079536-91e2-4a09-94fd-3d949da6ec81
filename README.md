# Table of Contents
- [Project Description](#project-description)
    - [KPI Parameter Processor](#kpi-parameter-processor)
    - [KPI-Param Transformation](#kpi-param-transformation)
    - [KPI Calculator](#kpi-calculator)
- [External ID Patterns](#external-id-patterns)
- [Adding new KPIs inside KPIDOM](#adding-new-kpis-inside-kpidom)
- [Workflows](#workflows)
- [Development Environment Setup](#development-environment-setup)
    - [Pre-Commit hook](#pre-commit-hook)
    - [Setting up the KPI Calculator Function or the KPI Processor Function](#setting-up-the-kpi-calculator-function-or-the-kpi-processor-function)

# Project Description

This project hosts all the necessary code to populate KPI data inside the KPIDOM data model.

The project consists of two functions:
- KPI Calculator: Responsible for calculating the KPI values for all business levels and time granularities
- KPI Parameter Processor: Responsible for refreshing kpi parameter values and calling the ***KPI Calculator*** as needed

It also contains workflow definitions used to populate the data models referenced by the KPIDOM transformations.

Here is a diagram showing how the kpis are calculated.
![KPIDOM Diagram](./.azuredevops/images/kpi-datapoints-diagram.png)

### KPI Parameter Processor
A function responsible for orchestrating the steps of the workflow.
It reads the data from the `REF-ALL-REFDATA.KPR-KPIParameter` raw table and find out which parameters should be reprocessed (based on a cron expression that tells how often it is ingested). After the "parameters due" are found the function deletes the data points for the parameters for a specified amount of days in the past.
The function then returns a json object containing:
- A list of tasks that should be executed by the workflow, dynamically. The tasks contain the transformations used to ingest data inside the parameters time series.
- A list of kpi codes that will need to be recalculated since their parameters will change

### KPI-Param Transformation
These transformations are reponsible for getting the data out of the "parent" data model and inside the time series of the parameters used to calculated the final value of the KPI. (For more information click [here](#parameter-ingestion-transformation))

### KPI Calculator
This is the core of the KPIDOM data model. This function is responsible for retrieving all the parameter time series data, all the formulas from the KPI view, and then use this information to dynamically calculate all the KPI values and store them in their resulting time series.


# External ID Patterns

All the `External Ids` in the project must follow a simple logic in order to be easly identifiable.
The pattern also allows a user to retrieve information without having to filter every property of a view.

## Data Model

### Reference Views
All the views used as reference have the following pattern:
`{VIEW-ACRONYM}-{CODE}`

**Examples**:
- `KPIG-FLD`: View `KpiGroup`, row code `FLD`
- `KPR-NM`: View `KpiParameter`, row code `NM`
- `KTG-QRT`: View `KpiTimeGranularity`, row code `QRT`

### Value Views
The views that store the values at different granularities and business levels have a pattern that allows all the information to be easily identifiable.

#### Parameter Value
`KPV-{PARAMETER_CODE}-{BUSINESS_LEVEL_CODE}-{SITE_CODE*}{BUSINESS_CODE}`
*\*The extra **SITE_CODE** is only added for the business segment level as way of making the row unique*

**Examples:**
- `KPV-RMBSKGM-BUS-SABEM`:
    - `RMBSKGM` is the KpiParameter code
    - `BUS` referes to BusinessSegmentLevel
    - `SAB` refers to the ReportingSite Sabine, River
    - `EM` refers to Engineering Material BusinessSegment
- `KPV-NM-UNT-BCHMON`:
    - `NM` is the parameter code
    - `UNT` referes to the Unit KpiBusinessLevel
    - `BCHMON` is the ReportingUnit code
- `KPV-PPCM-STS-FRA`:
    - `PPCM` is the parameter code
    - `STS` referes to the Site business level
    - `FRA` is the site code

#### KPI Result
`KRS-{KPI_CODE}-{BUSINESS_LEVEL_CODE}-{SITE_CODE*}{BUSINESS_CODE}-{TIME_GRANULARITY_CODE}`

**Examples:**
- `KRS-TCT-UNT-LUXPOL-MON`
    - `TCT`: Kpi Code
    - `UNT`: Unit KPIBusinessLevel code
    - `LUXPOL`: ReportingUnit Code
    - `MON`: Monthly Time granularity code

## Time Series

### Parameter Time Series
The time series also follows a pattern similar to the KpiParameter view id:
`KPI-PARAM:{PARAMETER_CODE}:{BUSINESS_LEVEL_CODE}:{SITE_CODE*}{BUSINESS_CODE}`
*\***SITE_CODE** is only added for the business segment level.*

**Examples:**
- `KPI-PARAM:RMBSKGM:BUS:SABEM`:
    - `RMBSKGM` is the parameter code
    - `BUS` referes to Business Segment level
    - `SAB` refers to the site Sabine, River
    - `EM` refers to Engineering Material segment
- `KPI-PARAM:NM:UNT:BCHMON`:
    - `NM` is the parameter code
    - `UNT` referes to the Unit business level
    - `BCHMON` is the unit code

Using this pattern a user could easily query data from cognite just with the time series read capability for the KPIDOM data-set and without having to access the data-model.

# Adding new KPIs inside KPIDOM

The KPIs inside the KPIDOM are designed in a data driven way, which means most of the work to configure a new KPI is done by adding new configuration data inside the KPIDOM domain tables.

The steps to configure a new KPI are:
## Define the KPI Info
To do this add a new row into this [spreadsheet](https://celanese.sharepoint.com/:x:/r/teams/DigitalPlantMVP/Shared%20Documents/Digital%20Plant%20Phase%203/Program%20Gemini/02.%20Pod%202%20-%20Data%20Model%20Development%20%26%20CDF%20Infrastructure/05.%20Knowledge%20Assets/03%20Data%20Model/01%20Reference%20Data%20Models/06%20KPIDOM/Reference%20Data%20KPIDOM.xlsx?d=wd182e7a550d84349942b56a8e93d84b5&csf=1&web=1&e=6FBcfV) at the KPIInfo sheet.

### `Group` Column
This tells which group the KPI belongs to. Check the data model or the KPI Group sheet to get the correct code for each group.

### `BusinessLevelColumn` Column
The **Business Level** column define at which business levels the KPI will be available. The KPI can have multiple business levels, to add more than one just separate the codes by commas. The current available levels are:
- Business Segment: BUS
- Reporting Site: STS
- Reporting Unit: UNT

### `TimeGranularity` Column
The **Time Granularity** column defined at which granularities the KPI will be available. The KPI can have multiple granularities available, to add more than one just separate the codes by commas. The current granularities available are:
- Daily: DAI
- Monthly: MON
- Quarterly: QRT
- Anually: ANL

### `Code` Column
A **small* acronym used to represent the KPI. This value must be unique accross the entire table.

### `Formula` Column
This is an integral part of the KPI calculation. This formula must follow a specific pattern in order to be recognized and used inside the calculation workflow.
The formula can contain any operation supported by a Pandas `DataFrame` and the parameters (variables) used in the calculation must be the code for the KPIs parameters wrapped around curly braces (see next section about [KPI Parameters](#Define-the-KPI's-Parameters)).
For example: `({PAR1}+{PAR2})/{PAR3}`

### `Currency`, `IsCurrencyUom`, `Uom` Columns
This column is only to define which "symbol" will be used when displaying the KPI in end-user-applicaions.

### `IsProtected` Column
This informs the calculation process if the KPI should be put inside a PROT space or a DAT space.

### `Owner` Column
The User's Principal Name of the person responsible for the KPI globally

## Define the KPI's Parameters
Parameters are the building blocks of the KPI. They hold the raw data used inside the formulas that calculate the final KPI value.

A parameter should always be stored at its lowest business level to make it possible to calculate the levels above. If the data is available at the unit level, for example, it's possible to add up the values from all the units of a given site and obtain the values for the site.

Define the parameters used to calculate the KPI inside the KPI-Parameter sheet on this [spreadsheet](https://celanese.sharepoint.com/:x:/r/teams/DigitalPlantMVP/Shared%20Documents/Digital%20Plant%20Phase%203/Program%20Gemini/02.%20Pod%202%20-%20Data%20Model%20Development%20%26%20CDF%20Infrastructure/05.%20Knowledge%20Assets/03%20Data%20Model/01%20Reference%20Data%20Models/06%20KPIDOM/Reference%20Data%20KPIDOM.xlsx?d=wd182e7a550d84349942b56a8e93d84b5&csf=1&web=1&e=6FBcfV).

### `LowestBusinessLevel` Column
The **CODE** of the lowest business level for the parameter.

### `KPI` Column
A comma separated list of KPI codes that tells which KPIs use this parameter.

### `DateRangeRefresh` Column
How many days in the past the parameters are allowed to change. This information is used to know how many days in the past should be erased and reprocessed into the parameters time series.

### `IngestionFrequency` Column
A cron expression that defines the frequency at which the parameters raw data is ingested into the system. It's a good idea to give some padding relative to the ingestion frequency to avoid missing data in case the ingestion process takes too long to execute.

### `IngestionTransformation` Column
The `Transformation` used to create the parameters' time series data points. See more details in the topic [Parameter Ingestion Transformation](#Parameter-Ingestion-Transformation).

## CDF Infrastructure Pull Request
After adding the necessary data inside the KPIDOM reference data spreadsheet a new Pull Request should be made to the `CDF Infrastructure` repository having the branch `ref_all_data` as the target, when this PR is completed a new set of PRs will be created pointing to the destination environments: staging and production.

## Parameter Ingestion Transformation

To feed data into KPIDOM calculations it's necessary to create time series where each data point is a record of the raw data.
The time series themselves are already created once the Pull Request to CDF Infrastructure is completed and their external IDs follow this pattern:
`KPI-PARAM:{PARAMETER_CODE}:{BUSINESS_LEVEL_CODE}:{SITE_CODE*}{BUSINESS_CODE}`
*\***SITE_CODE** is only added for the business segment level.*

Examples are:
- `KPI-PARAM:RMBSKGM:BUS:SABEM`:
    - `RMBSKGM` is the parameter code
    - `BUS` referes to Business Segment level
    - `SAB` refers to the site Sabine, River
    - `EM` refers to Engineering Material segment
- `KPI-PARAM:NM:UNT:BCHMON`:
    - `NM` is the parameter code
    - `UNT` referes to the Unit business level
    - `BCHMON` is the unit code

A transformation should be used to create the data points getting the data from the origin Data Model.

**Notes:**
- All the data points are stored in a naive timezone way, meaning all the data are stored assuming the timestamp is in the Site's timezone, so there is no need to convert time zones to UTC before saving it in the data point.
- If a data point represents data for an entire day, month, or year it's recommend to write the data at 12PM UTC to avoid confusion when looking at the charts in the Cognite portal.

Here is an example transformation
```sql=
SELECT
   concat("KPI-PARAM:PARAM_CODE:UNT:", unitCode) AS externalId -- The external Id of the time seris
  ,p.someValue as value -- The data point value
  ,cast(concat(year, "-01-01T12:00:00+00:00") AS timestamp) AS timestamp -- The data point timestamp
FROM cdf_data_models("SPACE", "DATA_MODEL", "1_0_0", "SomeTable") AS p
```

# Workflows

## Creating a Workflow
To **create** a new workflow, add the corresponding configuration JSON file to the `workflows/` directory.

## Modifying a Workflow
To **modify** an existing workflow:
1. Update the configuration JSON file.
2. Increment the workflow version.

## Deleting a Workflow
If a workflow configuration file is **deleted** from the `workflows/` directory, the workflow will also be deleted from Cognite.

## Important Notes
- The file name **must match** the `external_id` of the workflow.
- The `external_id` must start with the prefix `wf-<PROJECT_CODE>` (e.g., `wf-EXEC`, `wf-KPI`, etc.).
- Workflow versions **can only be incremented**.
- If a newer version of a workflow is already published, the pipeline will throw an error.
- When a workflow is updated to a new version, **all triggers from previous versions are deleted**.
- Currently, only workflows with the following project codes are configured (More prefixes can be added in [deploy_cognite_workflow.py](/cognite-resource-deployment/deploy_cognite_workflow.py)):
  - `EXEC`
  - `KPI`

# Development Environment Setup

## Pre-Commit hook

Before beginning the development, first run the setup script
`python setup.py`

This script will ensure all validations, that also run in the pipeline, are applied before the a new commit is created.

## Setting up the KPI Calculator Function or the KPI Processor Function

### To start the local development environment follow these steps:

1. Create and activate a virtual environment (This step is only needed once)
```bash
cd ./kpi-calculator-function # OR cd ./kpi-parameter-processor-function
python -m venv .venv
source ./.venv/bin/activate # if you are running on Linux or Mac
.\Scripts\activate # if you are running on Windows
```

2. Install Dependencies (This step is only needed once, unless new dependencies are installed)
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt # Necessary for local development (tests, linting, formatting, etc.)
```

3. Create a `.env.local` file and fill in the missing keys. (This file is not commited in the repository)

4. Run the function
    a. Run using the terminal
    ```bash
    python ./handler.py
    ```

    b. To run using VSCode first choose "Cognite function" and then presse F5


### Running tests

To execute all the tests run:
```bash
pytest .
```

To execute a specific test use the flag `-k` to filter by some string (The filter is equivalent to a contains)
```bash
pytest -k some_query_string .
```

To debug a single test file select "PyTest: Test Current File" in the VSCode debugger and press F5
