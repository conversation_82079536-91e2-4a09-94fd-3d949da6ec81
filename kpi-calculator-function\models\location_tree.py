from pydantic import BaseModel, Field


class LocationTreeConnector(BaseModel):
    id: int
    code: str
    description: str | None


class LocationTreeParentNode(BaseModel):
    id: int
    code: str
    description: str | None


class LocationTreeNode(BaseModel):
    id: int
    code: str
    description: str | None
    inactive: bool
    tree_connector: LocationTreeConnector = Field(alias="treeConnector")
    parent: LocationTreeParentNode | None
    end_date: str | None = Field(alias="endDate")


class LocationTreeReponse(BaseModel):
    total_count: int = Field(alias="totalCount")
    page_size: int = Field(alias="pageSize")
    page_index: int = Field(alias="pageIndex")
    start_at: int = Field(alias="startAt")
    records: list[LocationTreeNode]
