import pymsteams
from pydantic import BaseModel


class TeamsNotificationInput(BaseModel):
    title: str
    text: str
    activity_messages: list[str]
    button_text: str | None = None
    button_action_link: str | None = None

    def to_card_sections(self):
        for activity_message in self.activity_messages:
            section = pymsteams.cardsection()
            section.activityText(activity_message)
            yield section
